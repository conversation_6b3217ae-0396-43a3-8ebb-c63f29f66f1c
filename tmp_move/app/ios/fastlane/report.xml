<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="0: default_platform" time="0.000268">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="1: build_app" time="5.301819">
        
          <failure message="/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/actions/actions_helper.rb:67:in &apos;Fastlane::Actions.execute_action&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/runner.rb:255:in &apos;block in Fastlane::Runner#execute_action&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/runner.rb:229:in &apos;Dir.chdir&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/runner.rb:229:in &apos;Fastlane::Runner#execute_action&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/runner.rb:157:in &apos;Fastlane::Runner#trigger_action_by_name&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/fast_file.rb:159:in &apos;Fastlane::FastFile#method_missing&apos;&#10;Fastfile:75:in &apos;block (2 levels) in Fastlane::FastFile#parsing_binding&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/lane.rb:41:in &apos;Fastlane::Lane#call&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/runner.rb:49:in &apos;block in Fastlane::Runner#execute&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/runner.rb:45:in &apos;Dir.chdir&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/runner.rb:45:in &apos;Fastlane::Runner#execute&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/lane_manager.rb:46:in &apos;Fastlane::LaneManager.cruise_lane&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/command_line_handler.rb:34:in &apos;Fastlane::CommandLineHandler.handle&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/commands_generator.rb:110:in &apos;block (2 levels) in Fastlane::CommandsGenerator#run&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/commander-4.6.0/lib/commander/command.rb:187:in &apos;Commander::Command#call&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/commander-4.6.0/lib/commander/command.rb:157:in &apos;Commander::Command#run&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/commander-4.6.0/lib/commander/runner.rb:444:in &apos;Commander::Runner#run_active_command&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane_core/lib/fastlane_core/ui/fastlane_runner.rb:124:in &apos;Commander::Runner#run!&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/commander-4.6.0/lib/commander/delegates.rb:18:in &apos;Commander::Delegates#run!&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/commands_generator.rb:363:in &apos;Fastlane::CommandsGenerator#run&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/commands_generator.rb:43:in &apos;Fastlane::CommandsGenerator.start&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/fastlane/lib/fastlane/cli_tools_distributor.rb:123:in &apos;Fastlane::CLIToolsDistributor.take_off&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/gems/fastlane-2.228.0/bin/fastlane:23:in &apos;&lt;top (required)&gt;&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/bin/fastlane:25:in &apos;Kernel#load&apos;&#10;/Users/<USER>/Documents/GitHub/rs_mobile/tmp_move/app/ios/vendor/bundle/ruby/3.4.0/bin/fastlane:25:in &apos;&lt;top (required)&gt;&apos;&#10;/opt/homebrew/lib/ruby/gems/3.4.0/gems/bundler-2.7.1/lib/bundler/cli/exec.rb:59:in &apos;Kernel.load&apos;&#10;/opt/homebrew/lib/ruby/gems/3.4.0/gems/bundler-2.7.1/lib/bundler/cli/exec.rb:59:in &apos;Bundler::CLI::Exec#kernel_load&apos;&#10;/opt/homebrew/lib/ruby/gems/3.4.0/gems/bundler-2.7.1/lib/bundler/cli/exec.rb:23:in &apos;Bundler::CLI::Exec#run&apos;&#10;/opt/homebrew/lib/ruby/gems/3.4.0/gems/bundler-2.7.1/lib/bundler/cli.rb:451:in &apos;Bundler::CLI#exec&apos;&#10;/opt/homebrew/lib/ruby/gems/3.4.0/gems/bundler-2.7.1/lib/bundler/vendor/thor/lib/thor/command.rb:28:in &apos;Bundler::Thor::Command#run&apos;&#10;/opt/homebrew/lib/ruby/gems/3.4.0/gems/bundler-2.7.1/lib/bundler/vendor/thor/lib/thor/invocation.rb:127:in &apos;Bundler::Thor::Invocation#invoke_command&apos;&#10;/opt/homebrew/lib/ruby/gems/3.4.0/gems/bundler-2.7.1/lib/bundler/vendor/thor/lib/thor.rb:538:in &apos;Bundler::Thor.dispatch&apos;&#10;/opt/homebrew/lib/ruby/gems/3.4.0/gems/bundler-2.7.1/lib/bundler/cli.rb:35:in &apos;Bundler::CLI.dispatch&apos;&#10;/opt/homebrew/lib/ruby/gems/3.4.0/gems/bundler-2.7.1/lib/bundler/vendor/thor/lib/thor/base.rb:584:in &apos;Bundler::Thor::Base::ClassMethods#start&apos;&#10;/opt/homebrew/lib/ruby/gems/3.4.0/gems/bundler-2.7.1/lib/bundler/cli.rb:29:in &apos;Bundler::CLI.start&apos;&#10;/opt/homebrew/lib/ruby/gems/3.4.0/gems/bundler-2.7.1/exe/bundle:28:in &apos;block in &lt;top (required)&gt;&apos;&#10;/opt/homebrew/lib/ruby/gems/3.4.0/gems/bundler-2.7.1/lib/bundler/friendly_errors.rb:118:in &apos;Bundler.with_friendly_errors&apos;&#10;/opt/homebrew/lib/ruby/gems/3.4.0/gems/bundler-2.7.1/exe/bundle:20:in &apos;&lt;top (required)&gt;&apos;&#10;/opt/homebrew/opt/ruby/bin/bundle:25:in &apos;Kernel#load&apos;&#10;/opt/homebrew/opt/ruby/bin/bundle:25:in &apos;&lt;main&gt;&apos;&#10;&#10;Error building the application - see the log above" />
        
      </testcase>
    
  </testsuite>
</testsuites>

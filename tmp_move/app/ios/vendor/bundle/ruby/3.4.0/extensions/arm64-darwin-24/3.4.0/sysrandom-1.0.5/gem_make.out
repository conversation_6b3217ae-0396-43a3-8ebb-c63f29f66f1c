current directory: /Users/<USER>/Documents/GitHub/rs_mobile/skilldeck_App/ios/vendor/bundle/ruby/3.4.0/gems/sysrandom-1.0.5/ext/sysrandom
/opt/homebrew/opt/ruby/bin/ruby extconf.rb
creating Makefile

current directory: /Users/<USER>/Documents/GitHub/rs_mobile/skilldeck_App/ios/vendor/bundle/ruby/3.4.0/gems/sysrandom-1.0.5/ext/sysrandom
make DESTDIR\= sitearchdir\=./.gem.20250813-21839-lqzk7i sitelibdir\=./.gem.20250813-21839-lqzk7i clean

current directory: /Users/<USER>/Documents/GitHub/rs_mobile/skilldeck_App/ios/vendor/bundle/ruby/3.4.0/gems/sysrandom-1.0.5/ext/sysrandom
make DESTDIR\= sitearchdir\=./.gem.20250813-21839-lqzk7i sitelibdir\=./.gem.20250813-21839-lqzk7i
compiling randombytes_sysrandom.c
compiling sysrandom_ext.c
linking shared-object sysrandom_ext.bundle

current directory: /Users/<USER>/Documents/GitHub/rs_mobile/skilldeck_App/ios/vendor/bundle/ruby/3.4.0/gems/sysrandom-1.0.5/ext/sysrandom
make DESTDIR\= sitearchdir\=./.gem.20250813-21839-lqzk7i sitelibdir\=./.gem.20250813-21839-lqzk7i install
/usr/bin/install -c -m 0755 sysrandom_ext.bundle ./.gem.20250813-21839-lqzk7i

current directory: /Users/<USER>/Documents/GitHub/rs_mobile/skilldeck_App/ios/vendor/bundle/ruby/3.4.0/gems/sysrandom-1.0.5/ext/sysrandom
make DESTDIR\= sitearchdir\=./.gem.20250813-21839-lqzk7i sitelibdir\=./.gem.20250813-21839-lqzk7i clean

current directory: /Users/<USER>/Documents/GitHub/rs_mobile/skilldeck_App/ios/vendor/bundle/ruby/3.4.0/gems/digest-crc-0.7.0/ext/digest
/opt/homebrew/opt/ruby/bin/ruby -rrubygems /Users/<USER>/Documents/GitHub/rs_mobile/skilldeck_App/ios/vendor/bundle/ruby/3.4.0/gems/rake-13.3.0/exe/rake RUBYARCHDIR\=/Users/<USER>/Documents/GitHub/rs_mobile/skilldeck_App/ios/vendor/bundle/ruby/3.4.0/extensions/arm64-darwin-24/3.4.0/digest-crc-0.7.0 RUBYLIBDIR\=/Users/<USER>/Documents/GitHub/rs_mobile/skilldeck_App/ios/vendor/bundle/ruby/3.4.0/extensions/arm64-darwin-24/3.4.0/digest-crc-0.7.0
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc12_3gpp.c
compiling crc12_3gpp_ext.c
linking shared-object crc12_3gpp_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc15.c
compiling crc15_ext.c
linking shared-object crc15_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16.c
compiling crc16_ext.c
linking shared-object crc16_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_ccitt.c
compiling crc16_ccitt_ext.c
linking shared-object crc16_ccitt_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_dnp.c
compiling crc16_dnp_ext.c
linking shared-object crc16_dnp_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_genibus.c
compiling crc16_genibus_ext.c
linking shared-object crc16_genibus_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_kermit.c
compiling crc16_kermit_ext.c
linking shared-object crc16_kermit_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_modbus.c
compiling crc16_modbus_ext.c
linking shared-object crc16_modbus_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_usb.c
compiling crc16_usb_ext.c
linking shared-object crc16_usb_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_x_25.c
compiling crc16_x_25_ext.c
linking shared-object crc16_x_25_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_xmodem.c
compiling crc16_xmodem_ext.c
linking shared-object crc16_xmodem_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_zmodem.c
compiling crc16_zmodem_ext.c
linking shared-object crc16_zmodem_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc24.c
compiling crc24_ext.c
linking shared-object crc24_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc32.c
compiling crc32_ext.c
linking shared-object crc32_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc32_bzip2.c
compiling crc32_bzip2_ext.c
linking shared-object crc32_bzip2_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc32_jam.c
compiling crc32_jam_ext.c
linking shared-object crc32_jam_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc32_mpeg.c
compiling crc32_mpeg_ext.c
linking shared-object crc32_mpeg_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc32_posix.c
compiling crc32_posix_ext.c
linking shared-object crc32_posix_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc32_xfer.c
compiling crc32_xfer_ext.c
linking shared-object crc32_xfer_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc32c.c
compiling crc32c_ext.c
linking shared-object crc32c_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc5.c
compiling crc5_ext.c
linking shared-object crc5_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc64.c
compiling crc64_ext.c
linking shared-object crc64_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc64_jones.c
compiling crc64_jones_ext.c
linking shared-object crc64_jones_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc64_nvme.c
compiling crc64_nvme_ext.c
linking shared-object crc64_nvme_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc64_xz.c
compiling crc64_xz_ext.c
linking shared-object crc64_xz_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc8.c
compiling crc8_ext.c
linking shared-object crc8_ext.bundle
/opt/homebrew/Cellar/ruby/3.4.4/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc8_1wire.c
compiling crc8_1wire_ext.c
linking shared-object crc8_1wire_ext.bundle

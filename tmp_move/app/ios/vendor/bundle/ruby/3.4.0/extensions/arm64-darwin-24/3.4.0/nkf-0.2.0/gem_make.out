current directory: /Users/<USER>/Documents/GitHub/rs_mobile/skilldeck_App/ios/vendor/bundle/ruby/3.4.0/gems/nkf-0.2.0/ext/nkf
/opt/homebrew/opt/ruby/bin/ruby extconf.rb
creating Makefile

current directory: /Users/<USER>/Documents/GitHub/rs_mobile/skilldeck_App/ios/vendor/bundle/ruby/3.4.0/gems/nkf-0.2.0/ext/nkf
make DESTDIR\= sitearchdir\=./.gem.20250813-21839-jif874 sitelibdir\=./.gem.20250813-21839-jif874 clean

current directory: /Users/<USER>/Documents/GitHub/rs_mobile/skilldeck_App/ios/vendor/bundle/ruby/3.4.0/gems/nkf-0.2.0/ext/nkf
make DESTDIR\= sitearchdir\=./.gem.20250813-21839-jif874 sitelibdir\=./.gem.20250813-21839-jif874
compiling nkf.c
linking shared-object nkf.bundle

current directory: /Users/<USER>/Documents/GitHub/rs_mobile/skilldeck_App/ios/vendor/bundle/ruby/3.4.0/gems/nkf-0.2.0/ext/nkf
make DESTDIR\= sitearchdir\=./.gem.20250813-21839-jif874 sitelibdir\=./.gem.20250813-21839-jif874 install
/usr/bin/install -c -m 0755 nkf.bundle ./.gem.20250813-21839-jif874

current directory: /Users/<USER>/Documents/GitHub/rs_mobile/skilldeck_App/ios/vendor/bundle/ruby/3.4.0/gems/nkf-0.2.0/ext/nkf
make DESTDIR\= sitearchdir\=./.gem.20250813-21839-jif874 sitelibdir\=./.gem.20250813-21839-jif874 clean

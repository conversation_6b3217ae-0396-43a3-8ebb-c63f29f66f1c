Unreleased Changes
------------------

1.1145.0 (2025-08-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1144.0 (2025-08-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1143.0 (2025-08-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1142.0 (2025-08-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1141.0 (2025-08-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1140.0 (2025-08-01)
------------------

* Feature - Added support for enumerating regions for  `Aws::ARCRegionswitch`.

1.1139.0 (2025-07-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1138.0 (2025-07-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1137.0 (2025-07-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1136.0 (2025-07-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1135.0 (2025-07-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1134.0 (2025-07-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1133.0 (2025-07-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1132.0 (2025-07-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1131.0 (2025-07-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1130.0 (2025-07-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1129.0 (2025-07-16)
------------------

* Feature - Added support for enumerating regions for  `Aws::BedrockAgentCoreControl`.

* Feature - Added support for enumerating regions for  `Aws::BedrockAgentCore`.

1.1128.0 (2025-07-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1127.0 (2025-07-15)
------------------

* Feature - Added support for enumerating regions for  `Aws::S3Vectors`.

1.1126.0 (2025-07-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1125.0 (2025-07-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1124.0 (2025-07-01)
------------------

* Feature - Added support for enumerating regions for  `Aws::Odb`.

1.1123.0 (2025-06-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1122.0 (2025-06-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1121.0 (2025-06-26)
------------------

* Feature - Added support for enumerating regions for  `Aws::KeyspacesStreams`.

1.1120.0 (2025-06-23)
------------------

* Feature - Added support for enumerating regions for  `Aws::WorkspacesInstances`.

1.1119.0 (2025-06-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1118.0 (2025-06-18)
------------------

* Feature - Added support for enumerating regions for  `Aws::AIOps`.

1.1117.0 (2025-06-17)
------------------

* Feature - Added support for enumerating regions for  `Aws::MPA`.

1.1116.0 (2025-06-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1115.0 (2025-06-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1114.0 (2025-06-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1113.0 (2025-06-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1112.0 (2025-06-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1111.0 (2025-06-04)
------------------

* Feature - Added support for enumerating regions for  `Aws::Evs`.

1.1110.0 (2025-06-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

* Feature - AWS SDK for Ruby no longer supports Ruby runtime versions 2.5 and 2.6.

1.1109.0 (2025-05-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1108.0 (2025-05-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1107.0 (2025-05-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.


1.1106.0 (2025-05-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1105.0 (2025-05-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1104.0 (2025-05-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1103.0 (2025-05-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1102.0 (2025-05-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1101.0 (2025-05-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1100.0 (2025-05-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1099.0 (2025-05-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1098.0 (2025-05-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1097.0 (2025-05-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1096.0 (2025-05-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1095.0 (2025-04-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1094.0 (2025-04-29)
------------------

* Feature - Added support for enumerating regions for  `Aws::SSMGuiConnect`.

1.1093.0 (2025-04-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1092.0 (2025-04-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1091.0 (2025-04-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1090.0 (2025-04-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1089.0 (2025-04-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1088.0 (2025-04-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1087.0 (2025-04-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1086.0 (2025-04-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1085.0 (2025-04-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1084.0 (2025-04-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1083.0 (2025-04-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1082.0 (2025-04-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1081.0 (2025-04-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1080.0 (2025-04-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1079.0 (2025-04-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1078.0 (2025-03-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1077.0 (2025-03-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1076.0 (2025-03-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1075.0 (2025-03-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1074.0 (2025-03-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1073.0 (2025-03-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1072.0 (2025-03-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1071.0 (2025-03-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1070.0 (2025-03-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1069.0 (2025-03-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1068.0 (2025-03-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1067.0 (2025-03-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1066.0 (2025-03-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1065.0 (2025-03-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1064.0 (2025-03-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1063.0 (2025-03-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1062.0 (2025-03-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1061.0 (2025-03-05)
------------------

* Feature - Added support for enumerating regions for  `Aws::GameLiftStreams`.

1.1060.0 (2025-03-04)
------------------

* Feature - Added support for enumerating regions for  `Aws::IoTManagedIntegrations`.

1.1059.0 (2025-03-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1058.0 (2025-02-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1057.0 (2025-02-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1056.0 (2025-02-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1055.0 (2025-02-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1054.0 (2025-02-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1053.0 (2025-02-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1052.0 (2025-02-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1051.0 (2025-02-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1050.0 (2025-02-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1049.0 (2025-02-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1048.0 (2025-02-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1047.0 (2025-02-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1046.0 (2025-02-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1045.0 (2025-02-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1044.0 (2025-01-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1043.0 (2025-01-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1042.0 (2025-01-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1041.0 (2025-01-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1040.0 (2025-01-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1039.0 (2025-01-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1038.0 (2025-01-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1037.0 (2025-01-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1036.0 (2025-01-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1035.0 (2025-01-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1034.0 (2025-01-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1033.0 (2025-01-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1032.0 (2025-01-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1031.0 (2025-01-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1030.0 (2025-01-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1029.0 (2024-12-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1028.0 (2024-12-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1027.0 (2024-12-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1026.0 (2024-12-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1025.0 (2024-12-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1024.0 (2024-12-17)
------------------

* Feature - Added support for enumerating regions for  `Aws::BackupSearch`.

1.1023.0 (2024-12-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1022.0 (2024-12-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1021.0 (2024-12-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1020.0 (2024-12-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1019.0 (2024-12-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1018.0 (2024-12-04)
------------------

* Feature - Added support for enumerating regions for  `Aws::BedrockDataAutomationRuntime`.

* Feature - Added support for enumerating regions for  `Aws::BedrockDataAutomation`.

1.1017.0 (2024-12-03)
------------------

* Feature - Added support for enumerating regions for  `Aws::S3Tables`.

* Feature - Added support for enumerating regions for  `Aws::DSQL`.

1.1016.0 (2024-12-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1015.0 (2024-12-02)
------------------

* Feature - Added support for enumerating regions for  `Aws::SecurityIR`.

* Feature - Added support for enumerating regions for  `Aws::NetworkFlowMonitor`.

* Feature - Added support for enumerating regions for  `Aws::Invoicing`.

1.1014.0 (2024-11-27)
------------------

* Feature - Added support for enumerating regions for  `Aws::ObservabilityAdmin`.

1.1013.0 (2024-11-22)
------------------

* Feature - Added support for enumerating regions for  `Aws::BCMPricingCalculator`.

1.1012.0 (2024-11-21)
------------------

* Feature - Added support for enumerating regions for  `Aws::NotificationsContacts`.

* Feature - Added support for enumerating regions for  `Aws::Notifications`.

1.1011.0 (2024-11-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1010.0 (2024-11-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1009.0 (2024-11-15)
------------------

* Feature - Added support for enumerating regions for  `Aws::ConnectCampaignsV2`.

1.1008.0 (2024-11-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1007.0 (2024-11-14)
------------------

* Feature - Added support for enumerating regions for  `Aws::PartnerCentralSelling`.

1.1006.0 (2024-11-13)
------------------

* Feature - Added support for enumerating regions for  `Aws::Billing`.

1.1005.0 (2024-11-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1004.0 (2024-11-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1003.0 (2024-11-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1002.0 (2024-11-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1001.0 (2024-11-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.1000.0 (2024-10-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.999.0 (2024-10-30)
------------------

* Feature - Added support for enumerating regions for  `Aws::GeoRoutes`.

* Feature - Added support for enumerating regions for  `Aws::GeoPlaces`.

* Feature - Added support for enumerating regions for  `Aws::GeoMaps`.

1.998.0 (2024-10-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.997.0 (2024-10-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.996.0 (2024-10-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.995.0 (2024-10-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.994.0 (2024-10-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.993.0 (2024-10-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.992.0 (2024-10-18)
------------------

* Feature - Add partition metadata module, allowing access without loading entire partitions.json.

1.991.0 (2024-10-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.990.0 (2024-10-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.989.0 (2024-10-10)
------------------

* Feature - Added support for enumerating regions for  `Aws::SocialMessaging`.

1.988.0 (2024-10-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.987.0 (2024-10-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.986.0 (2024-10-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.985.0 (2024-10-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.984.0 (2024-10-03)
------------------

* Feature - Added support for enumerating regions for  `Aws::MarketplaceReporting`.

1.983.0 (2024-10-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.982.0 (2024-10-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.981.0 (2024-09-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.980.0 (2024-09-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.979.0 (2024-09-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.978.0 (2024-09-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.977.0 (2024-09-18)
------------------

* Feature - Added support for enumerating regions for  `Aws::DirectoryServiceData`.

1.976.0 (2024-09-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.975.0 (2024-09-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.974.0 (2024-09-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.973.0 (2024-09-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.972.0 (2024-09-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.971.0 (2024-09-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.970.0 (2024-08-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.969.0 (2024-08-28)
------------------

* Feature - Added support for enumerating regions for  `Aws::PCS`.

1.968.0 (2024-08-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.967.0 (2024-08-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.966.0 (2024-08-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.965.0 (2024-08-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.964.0 (2024-08-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.963.0 (2024-08-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.962.0 (2024-08-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.961.0 (2024-08-01)
------------------

* Feature - Added support for enumerating regions for  `Aws::SSMQuickSetup`.

1.960.0 (2024-07-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.959.0 (2024-07-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.958.0 (2024-07-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.957.0 (2024-07-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.956.0 (2024-07-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.955.0 (2024-07-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.954.0 (2024-07-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.953.0 (2024-07-08)
------------------

* Feature - Added support for enumerating regions for  `Aws::QApps`.

1.952.0 (2024-07-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.951.0 (2024-07-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.950.0 (2024-07-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.949.0 (2024-06-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.948.0 (2024-06-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.947.0 (2024-06-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.946.0 (2024-06-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.945.0 (2024-06-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.944.0 (2024-06-12)
------------------

* Feature - Added support for enumerating regions for  `Aws::AppTest`.

1.943.0 (2024-06-11)
------------------

* Feature - Added support for enumerating regions for  `Aws::PcaConnectorScep`.

1.942.0 (2024-06-10)
------------------

* Feature - Added support for enumerating regions for  `Aws::ApplicationSignals`.

1.941.0 (2024-06-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.940.0 (2024-06-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.939.0 (2024-06-04)
------------------

* Feature - Added support for enumerating regions for  `Aws::TaxSettings`.

1.938.0 (2024-06-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.937.0 (2024-05-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.936.0 (2024-05-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.935.0 (2024-05-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.934.0 (2024-05-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.933.0 (2024-05-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.932.0 (2024-05-21)
------------------

* Feature - Added support for enumerating regions for  `Aws::MailManager`.

1.931.0 (2024-05-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.930.0 (2024-05-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.929.0 (2024-05-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.928.0 (2024-05-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.927.0 (2024-05-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.926.0 (2024-05-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.925.0 (2024-05-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.924.0 (2024-05-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.923.0 (2024-05-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.922.0 (2024-04-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.921.0 (2024-04-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.920.0 (2024-04-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.919.0 (2024-04-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.918.0 (2024-04-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.917.0 (2024-04-22)
------------------

* Feature - Added support for enumerating regions for  `Aws::Route53Profiles`.

1.916.0 (2024-04-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.915.0 (2024-04-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.914.0 (2024-04-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.913.0 (2024-04-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.912.0 (2024-04-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.911.0 (2024-04-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.910.0 (2024-04-08)
------------------

* Feature - Added support for enumerating regions for  `Aws::ControlCatalog`.

1.909.0 (2024-04-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.908.0 (2024-04-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.907.0 (2024-04-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.906.0 (2024-04-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.905.0 (2024-04-01)
------------------

* Feature - Added support for enumerating regions for  `Aws::Deadline`.

1.904.0 (2024-03-29)
------------------

* Feature - Added support for enumerating regions for  `Aws::CodeConnections`.

1.903.0 (2024-03-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.902.0 (2024-03-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.901.0 (2024-03-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.900.0 (2024-03-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.899.0 (2024-03-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.898.0 (2024-03-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.897.0 (2024-03-14)
------------------

* Feature - Added support for enumerating regions for  `Aws::TimestreamInfluxDB`.

1.896.0 (2024-03-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.895.0 (2024-02-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.894.0 (2024-02-19)
------------------

* Feature - Added support for enumerating regions for  `Aws::Chatbot`.

1.893.0 (2024-02-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.892.0 (2024-02-15)
------------------

* Feature - Added support for enumerating regions for  `Aws::Artifact`.

1.891.0 (2024-02-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.890.0 (2024-02-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.889.0 (2024-02-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.888.0 (2024-02-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.887.0 (2024-02-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.886.0 (2024-02-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.885.0 (2024-01-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.884.0 (2024-01-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.883.0 (2024-01-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.882.0 (2024-01-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.881.0 (2024-01-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.880.0 (2024-01-12)
------------------

* Feature - Added support for enumerating regions for  `Aws::SupplyChain`.

1.879.0 (2024-01-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.878.0 (2024-01-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.877.0 (2024-01-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.876.0 (2023-12-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.875.0 (2023-12-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.874.0 (2023-12-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.873.0 (2023-12-22)
------------------

* Feature - Added support for enumerating regions for `Aws::NetworkMonitor`.

1.872.0 (2023-12-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.871.0 (2023-12-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.870.0 (2023-12-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.869.0 (2023-12-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.868.0 (2023-12-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.867.0 (2023-12-14)
------------------

* Feature - Added support for enumerating regions for `Aws::NeptuneGraph`.

1.866.0 (2023-12-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.865.0 (2023-12-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.864.0 (2023-12-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.863.0 (2023-12-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.862.0 (2023-11-30)
------------------

* Feature - Added support for enumerating regions for `Aws::MarketplaceDeployment`.

* Feature - Added support for enumerating regions for `Aws::MarketplaceAgreement`.

1.861.0 (2023-11-29)
------------------

* Feature - Added support for enumerating regions for `Aws::CleanRoomsML`.

1.860.0 (2023-11-28)
------------------

* Feature - Added support for enumerating regions for `Aws::QConnect`.

* Feature - Added support for enumerating regions for `Aws::QBusiness`.

* Feature - Added support for enumerating regions for `Aws::BedrockAgentRuntime`.

* Feature - Added support for enumerating regions for `Aws::BedrockAgent`.

1.859.0 (2023-11-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.858.0 (2023-11-27)
------------------

* Feature - Added support for enumerating regions for `Aws::B2bi`.

1.857.0 (2023-11-27)
------------------

* Feature - Added support for enumerating regions for `Aws::WorkSpacesThinClient`.

* Feature - Added support for enumerating regions for `Aws::Repostspace`.

* Feature - Added support for enumerating regions for `Aws::FreeTier`.

* Feature - Added support for enumerating regions for `Aws::EKSAuth`.

* Feature - Added support for enumerating regions for `Aws::CostOptimizationHub`.

* Feature - Added support for enumerating regions for `Aws::BCMDataExports`.

1.856.0 (2023-11-22)
------------------

* Feature - AWS SDK for Ruby no longer supports Ruby runtime versions 2.3 and 2.4.

1.855.0 (2023-11-21)
------------------

* Feature - Added support for enumerating regions for `Aws::InspectorScan`.

* Feature - Added support for enumerating regions for `Aws::CloudFrontKeyValueStore`.

1.854.0 (2023-11-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.853.0 (2023-11-17)
------------------

* Feature - Added support for enumerating regions for `Aws::TrustedAdvisor`.

1.852.0 (2023-11-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.851.0 (2023-11-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.850.0 (2023-11-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.849.0 (2023-11-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.848.0 (2023-11-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.847.0 (2023-11-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.846.0 (2023-11-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.845.0 (2023-11-03)
------------------

* Feature - Added support for enumerating regions for `Aws::LaunchWizard`.

1.844.0 (2023-11-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.843.0 (2023-10-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.842.0 (2023-10-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.841.0 (2023-10-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.840.0 (2023-10-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.839.0 (2023-10-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.838.0 (2023-10-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.837.0 (2023-10-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.836.0 (2023-10-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.835.0 (2023-10-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.834.0 (2023-10-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.833.0 (2023-10-04)
------------------

* Feature - Added support for enumerating regions for `Aws::DataZone`.

1.832.0 (2023-10-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.831.0 (2023-10-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.830.0 (2023-09-28)
------------------

* Feature - Added support for enumerating regions for `Aws::BedrockRuntime`.

* Feature - Added support for enumerating regions for `Aws::Bedrock`.

1.829.0 (2023-09-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.828.0 (2023-09-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.827.0 (2023-09-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.826.0 (2023-09-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.825.0 (2023-09-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.824.0 (2023-09-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.823.0 (2023-09-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.822.0 (2023-09-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.821.0 (2023-09-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.820.0 (2023-09-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.819.0 (2023-09-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.818.0 (2023-09-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.817.0 (2023-09-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.816.0 (2023-09-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.815.0 (2023-08-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.814.0 (2023-08-30)
------------------

* Feature - Added support for enumerating regions for `Aws::PcaConnectorAd`.

* Feature - Added support for enumerating regions for `Aws::Neptunedata`.

1.813.0 (2023-08-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.812.0 (2023-08-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.811.0 (2023-08-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.810.0 (2023-08-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.809.0 (2023-08-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.808.0 (2023-08-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.807.0 (2023-08-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.806.0 (2023-08-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.805.0 (2023-08-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.804.0 (2023-08-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.803.0 (2023-08-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.802.0 (2023-08-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.801.0 (2023-08-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.800.0 (2023-08-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.799.0 (2023-08-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.798.0 (2023-08-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.797.0 (2023-08-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.796.0 (2023-08-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.795.0 (2023-07-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.794.0 (2023-07-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.793.0 (2023-07-26)
------------------

* Feature - Added support for enumerating regions for `Aws::ManagedBlockchainQuery`.

* Feature - Added support for enumerating regions for `Aws::EntityResolution`.

1.792.0 (2023-07-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.791.0 (2023-07-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.790.0 (2023-07-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.789.0 (2023-07-19)
------------------

* Feature - Added support for enumerating regions for `Aws::MedicalImaging`.

1.788.0 (2023-07-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.787.0 (2023-07-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.786.0 (2023-07-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.785.0 (2023-07-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.784.0 (2023-07-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.783.0 (2023-07-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.782.0 (2023-06-27)
------------------

* Feature - Added support for enumerating regions for `Aws::AppFabric`.

1.781.0 (2023-06-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.780.0 (2023-06-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.779.0 (2023-06-13)
------------------

* Feature - Added support for enumerating regions for `Aws::VerifiedPermissions`.

* Feature - Added support for enumerating regions for `Aws::CodeGuruSecurity`.

1.778.0 (2023-06-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.777.0 (2023-06-08)
------------------

* Feature - Added support for enumerating regions for `Aws::PaymentCryptographyData`.

* Feature - Added support for enumerating regions for `Aws::PaymentCryptography`.

1.776.0 (2023-06-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.775.0 (2023-06-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.774.0 (2023-06-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.773.0 (2023-06-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.772.0 (2023-05-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.771.0 (2023-05-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.770.0 (2023-05-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.769.0 (2023-05-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.768.0 (2023-05-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.767.0 (2023-05-19)
------------------

* Feature - Added support for enumerating regions for `Aws::MediaPackageV2`.

1.766.0 (2023-05-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.765.0 (2023-05-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.764.0 (2023-05-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.763.0 (2023-05-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.762.0 (2023-05-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.761.0 (2023-05-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.760.0 (2023-05-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.759.0 (2023-05-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.758.0 (2023-05-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.757.0 (2023-04-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.756.0 (2023-04-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.755.0 (2023-04-26)
------------------

* Feature - Added support for enumerating regions for `Aws::OSIS`.

1.754.0 (2023-04-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.753.0 (2023-04-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.752.0 (2023-04-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.751.0 (2023-04-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.750.0 (2023-04-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.749.0 (2023-04-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.748.0 (2023-04-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.747.0 (2023-04-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.746.0 (2023-04-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.745.0 (2023-04-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.744.0 (2023-04-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.743.0 (2023-04-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.742.0 (2023-04-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.741.0 (2023-04-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.740.0 (2023-04-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.739.0 (2023-03-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.738.0 (2023-03-30)
------------------

* Feature - Added support for enumerating regions for `Aws::VPCLattice`.

1.737.0 (2023-03-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.736.0 (2023-03-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.735.0 (2023-03-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.734.0 (2023-03-23)
------------------

* Feature - Added support for enumerating regions for `Aws::IVSRealTime`.

1.733.0 (2023-03-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.732.0 (2023-03-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.731.0 (2023-03-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.730.0 (2023-03-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.729.0 (2023-03-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.728.0 (2023-03-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.727.0 (2023-03-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.726.0 (2023-03-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.725.0 (2023-03-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.724.0 (2023-03-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.723.0 (2023-03-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.722.0 (2023-03-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.721.0 (2023-03-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.720.0 (2023-03-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.719.0 (2023-03-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.718.0 (2023-02-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.717.0 (2023-02-27)
------------------

* Feature - Added support for enumerating regions for `Aws::InternetMonitor`.

1.716.0 (2023-02-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.715.0 (2023-02-21)
------------------

* Feature - Added support for enumerating regions for `Aws::Tnb`.

1.714.0 (2023-02-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.713.0 (2023-02-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.712.0 (2023-02-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.711.0 (2023-02-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.710.0 (2023-02-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.709.0 (2023-02-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.708.0 (2023-02-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.707.0 (2023-02-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.706.0 (2023-02-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.705.0 (2023-02-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.704.0 (2023-02-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.703.0 (2023-02-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.702.0 (2023-01-31)
------------------

* Feature - Added support for enumerating regions for `Aws::CloudTrailData`.

1.701.0 (2023-01-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.700.0 (2023-01-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.699.0 (2023-01-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.698.0 (2023-01-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.697.0 (2023-01-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.696.0 (2023-01-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.695.0 (2023-01-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.694.0 (2023-01-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.693.0 (2023-01-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.692.0 (2023-01-12)
------------------

* Feature - Added support for enumerating regions for `Aws::CleanRooms`.

1.691.0 (2023-01-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.690.0 (2023-01-09)
------------------

* Feature - Added support for enumerating regions for `Aws::KendraRanking`.

1.689.0 (2023-01-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.688.0 (2023-01-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.687.0 (2023-01-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.686.0 (2022-12-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.685.0 (2022-12-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.684.0 (2022-12-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.683.0 (2022-12-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.682.0 (2022-12-21)
------------------

* Feature - Added support for enumerating regions for `Aws::LicenseManagerLinuxSubscriptions`.

1.681.0 (2022-12-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.680.0 (2022-12-19)
------------------

* Feature - Added support for enumerating regions for `Aws::KinesisVideoWebRTCStorage`.

1.679.0 (2022-12-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.678.0 (2022-12-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.677.0 (2022-12-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.676.0 (2022-12-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.675.0 (2022-12-12)
------------------

* Feature - Added support for enumerating regions for `Aws::SageMakerMetrics`.

1.674.0 (2022-12-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.673.0 (2022-12-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.672.0 (2022-12-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.671.0 (2022-12-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.670.0 (2022-12-01)
------------------

* Feature - Added support for enumerating regions for `Aws::Pipes`.

* Feature - Added support for enumerating regions for `Aws::CodeCatalyst`.

1.669.0 (2022-11-30)
------------------

* Feature - Added support for enumerating regions for `Aws::SageMakerGeospatial`.

* Feature - Added support for enumerating regions for `Aws::DocDBElastic`.

1.668.0 (2022-11-29)
------------------

* Feature - Added support for enumerating regions for `Aws::SimSpaceWeaver`.

* Feature - Added support for enumerating regions for `Aws::SecurityLake`.

* Feature - Added support for enumerating regions for `Aws::OpenSearchServerless`.

* Feature - Added support for enumerating regions for `Aws::Omics`.

1.667.0 (2022-11-29)
------------------

* Feature - Added support for enumerating regions for `Aws::ARCZonalShift`.

1.666.0 (2022-11-28)
------------------

* Feature - Added support for enumerating regions for `Aws::OAM`.

1.665.0 (2022-11-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.664.0 (2022-11-18)
------------------

* Feature - Added support for enumerating regions for `Aws::IoTRoboRunner`.

* Feature - Added support for enumerating regions for `Aws::ChimeSDKVoice`.

1.663.0 (2022-11-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.662.0 (2022-11-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.661.0 (2022-11-15)
------------------

* Feature - Added support for enumerating regions for `Aws::SsmSap`.

1.660.0 (2022-11-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.659.0 (2022-11-10)
------------------

* Feature - Added support for enumerating regions for `Aws::Scheduler`.

1.658.0 (2022-11-08)
------------------

* Feature - Added support for enumerating regions for `Aws::ResourceExplorer2`.

1.657.0 (2022-11-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.656.0 (2022-11-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.655.0 (2022-11-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.654.0 (2022-10-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.653.0 (2022-10-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.652.0 (2022-10-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.651.0 (2022-10-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

* Feature - Add a metadata method to `Partition` to supplement endpoint generation in service gems.

1.650.0 (2022-10-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.649.0 (2022-10-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.648.0 (2022-10-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.647.0 (2022-10-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.646.0 (2022-10-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.645.0 (2022-10-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.644.0 (2022-10-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.643.0 (2022-10-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.642.0 (2022-10-04)
------------------

* Feature - Added support for enumerating regions for `Aws::ConnectCases`.

1.641.0 (2022-10-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.640.0 (2022-09-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.639.0 (2022-09-29)
------------------

* Feature - Added support for enumerating regions for `Aws::MigrationHubOrchestrator`.

1.638.0 (2022-09-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.637.0 (2022-09-26)
------------------

* Feature - Added support for enumerating regions for `Aws::IoTFleetWise`.

1.636.0 (2022-09-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.635.0 (2022-09-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.634.0 (2022-09-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.633.0 (2022-09-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.632.0 (2022-09-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.631.0 (2022-09-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.630.0 (2022-09-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.629.0 (2022-09-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.628.0 (2022-09-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.627.0 (2022-09-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.626.0 (2022-09-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.625.0 (2022-09-01)
------------------

* Feature - Added support for enumerating regions for `Aws::ControlTower`.

1.624.0 (2022-08-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.623.0 (2022-08-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.622.0 (2022-08-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.621.0 (2022-08-22)
------------------

* Feature - Added support for enumerating regions for `Aws::SupportApp`.

1.620.0 (2022-08-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.619.0 (2022-08-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.618.0 (2022-08-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.617.0 (2022-08-11)
------------------

* Feature - Added support for enumerating regions for `Aws::PrivateNetworks`.

* Feature - Added support for enumerating regions for `Aws::BackupStorage`.

1.616.0 (2022-08-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.615.0 (2022-08-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.614.0 (2022-08-02)
------------------

* Feature - Added support for enumerating regions for `Aws::LicenseManagerUserSubscriptions`.

1.613.0 (2022-07-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.612.0 (2022-07-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.611.0 (2022-07-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.610.0 (2022-07-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.609.0 (2022-07-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.608.0 (2022-07-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.607.0 (2022-07-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.606.0 (2022-07-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.605.0 (2022-07-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.604.0 (2022-07-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.603.0 (2022-07-05)
------------------

* Feature - Added support for enumerating regions for `Aws::RolesAnywhere`.

1.602.0 (2022-06-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.601.0 (2022-06-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.600.0 (2022-06-17)
------------------

* Feature - Added support for enumerating regions for `Aws::ConnectCampaignService`.

1.599.0 (2022-06-16)
------------------

* Feature - Added support for enumerating regions for `Aws::RedshiftServerless`.

1.598.0 (2022-06-08)
------------------

* Feature - Added support for enumerating regions for `Aws::RedshiftServerless`.

* Feature - Added support for enumerating regions for `Aws::MainframeModernization`.

1.597.0 (2022-06-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.596.0 (2022-06-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.595.0 (2022-05-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.594.0 (2022-05-27)
------------------

* Feature - Added support for enumerating regions for `Aws::EMRServerless`.

1.593.0 (2022-05-26)
------------------

* Feature - Added support for enumerating regions for `Aws::EMRServerlessWebService`.

1.592.0 (2022-05-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.591.0 (2022-05-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.590.0 (2022-05-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.589.0 (2022-05-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.588.0 (2022-05-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.587.0 (2022-05-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.586.0 (2022-05-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.585.0 (2022-05-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.584.0 (2022-05-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.583.0 (2022-05-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.582.0 (2022-05-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.581.0 (2022-04-27)
------------------

* Feature - Added support for enumerating regions for `Aws::ChimeSDKMediaPipelines`.

1.580.0 (2022-04-26)
------------------

* Feature - Added support for enumerating regions for `Aws::Ivschat`.

1.579.0 (2022-04-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.578.0 (2022-04-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.577.0 (2022-04-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.576.0 (2022-04-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.575.0 (2022-04-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.574.0 (2022-04-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.573.0 (2022-04-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.572.0 (2022-03-31)
------------------

* Feature - Added support for enumerating regions for `Aws::PinpointSMSVoiceV2`.

1.571.0 (2022-03-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.570.0 (2022-03-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.569.0 (2022-03-23)
------------------

* Feature - Added support for enumerating regions for `Aws::GameSparks`.

1.568.0 (2022-03-16)
------------------

* Feature - Added support for enumerating regions for `Aws::BillingConductor`.

1.567.0 (2022-03-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.566.0 (2022-03-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.565.0 (2022-03-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.564.0 (2022-03-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.563.0 (2022-03-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.562.0 (2022-03-02)
------------------

* Feature - Added support for enumerating regions for `Aws::Keyspaces`.

1.561.0 (2022-03-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.560.0 (2022-02-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.559.0 (2022-02-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.558.0 (2022-02-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.557.0 (2022-02-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.556.0 (2022-02-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.555.0 (2022-02-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.554.0 (2022-02-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.553.0 (2022-02-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.552.0 (2022-02-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.551.0 (2022-01-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.550.0 (2022-01-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.549.0 (2022-01-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.548.0 (2022-01-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.547.0 (2022-01-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.546.0 (2022-01-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.545.0 (2022-01-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.544.0 (2022-01-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.543.0 (2021-12-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.542.0 (2021-12-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.541.0 (2021-12-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.540.0 (2021-12-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.539.0 (2021-12-02)
------------------

* Feature - Added support for enumerating regions for `Aws::AmplifyUIBuilder`.

1.538.0 (2021-12-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.537.0 (2021-11-30)
------------------

* Feature - Added support for enumerating regions for `Aws::WorkSpacesWeb`.

* Feature - Added support for enumerating regions for `Aws::IoTTwinMaker`.

* Feature - Added support for enumerating regions for `Aws::BackupGateway`.

1.536.0 (2021-11-29)
------------------

* Feature - Added support for enumerating regions for `Aws::RecycleBin`.

* Feature - Added support for enumerating regions for `Aws::Inspector2`.

* Feature - Added support for enumerating regions for `Aws::CloudWatchRUM`.

* Feature - Added support for enumerating regions for `Aws::CloudWatchEvidently`.

1.535.0 (2021-11-29)
------------------

* Feature - Added support for enumerating regions for `Aws::MigrationHubRefactorSpaces`.

1.534.0 (2021-11-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.533.0 (2021-11-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.532.0 (2021-11-17)
------------------

* Feature - Added support for enumerating regions for `Aws::Drs`.

* Feature - Added support for enumerating regions for `Aws::AppConfigData`.

1.531.0 (2021-11-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.530.0 (2021-11-15)
------------------

* Feature - Added support for enumerating regions for `Aws::MigrationHubStrategyRecommendations`.

1.529.0 (2021-11-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.528.0 (2021-11-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.527.0 (2021-11-10)
------------------

* Feature - Added support for enumerating regions for `Aws::ResilienceHub`.

1.526.0 (2021-11-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.525.0 (2021-11-04)
------------------

* Feature - Added support for enumerating regions for `Aws::ChimeSDKMeetings`.

* Feature - Support modeled dualstack and fips endpoints in `Aws::Partitions::EndpointProvider`.

1.524.0 (2021-11-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.523.0 (2021-11-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.522.0 (2021-11-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.521.0 (2021-10-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.520.1 (2021-10-28)
------------------

* Issue - Add `signing_service` method and resolve `credentialScope` correctly for global services/service defaults.

1.520.0 (2021-10-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.519.0 (2021-10-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.518.0 (2021-10-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.517.0 (2021-10-20)
------------------

* Feature - Added support for enumerating regions for `Aws::Panorama`.

1.516.0 (2021-10-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.515.0 (2021-10-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.514.0 (2021-10-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.513.0 (2021-10-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.512.0 (2021-10-07)
------------------

* Feature - Added support for enumerating regions for `Aws::ManagedGrafana`.

1.511.0 (2021-10-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.510.0 (2021-10-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.509.0 (2021-09-30)
------------------

* Feature - Added support for enumerating regions for `Aws::CloudControlApi`.

* Feature - Added support for enumerating regions for `Aws::Account`.

1.508.0 (2021-09-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.507.0 (2021-09-27)
------------------

* Feature - Added support for enumerating regions for `Aws::VoiceID`.

* Feature - Added support for enumerating regions for `Aws::ConnectWisdomService`.

1.506.0 (2021-09-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.505.0 (2021-09-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.504.0 (2021-09-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.503.0 (2021-09-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.502.0 (2021-09-16)
------------------

* Feature - Added support for enumerating regions for `Aws::KafkaConnect`.

1.501.0 (2021-09-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.500.0 (2021-09-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.499.0 (2021-09-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.498.0 (2021-09-08)
------------------

* Feature - Added support for enumerating regions for `Aws::OpenSearchService`.

1.497.0 (2021-09-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.496.0 (2021-09-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.495.0 (2021-09-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.494.0 (2021-09-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

* Feature - AWS SDK for Ruby no longer supports Ruby runtime versions 1.9, 2.0, 2.1, and 2.2.

1.493.0 (2021-08-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.492.0 (2021-08-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.491.0 (2021-08-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.490.0 (2021-08-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.489.0 (2021-08-19)
------------------

* Feature - Added support for enumerating regions for `Aws::MemoryDB`.

1.488.0 (2021-08-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.487.0 (2021-08-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.486.0 (2021-08-11)
------------------

* Feature - Added support for enumerating regions for `Aws::SnowDeviceManagement`.

1.485.0 (2021-08-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.484.0 (2021-08-06)
------------------

* Feature - Added support for enumerating regions for `Aws::ChimeSDKMessaging`.

* Feature - Added support for enumerating regions for `Aws::ChimeSDKIdentity`.

1.483.0 (2021-08-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.482.0 (2021-07-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.481.0 (2021-07-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.480.0 (2021-07-27)
------------------

* Feature - Added support for enumerating regions for `Aws::Route53RecoveryReadiness`.

* Feature - Added support for enumerating regions for `Aws::Route53RecoveryControlConfig`.

* Feature - Added support for enumerating regions for `Aws::Route53RecoveryCluster`.

1.479.0 (2021-07-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.478.0 (2021-07-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.477.0 (2021-07-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.476.0 (2021-07-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.475.0 (2021-07-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.474.0 (2021-07-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.473.0 (2021-07-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.472.0 (2021-06-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.471.0 (2021-06-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.470.0 (2021-06-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.469.0 (2021-06-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.468.0 (2021-06-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.467.0 (2021-06-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.466.0 (2021-06-09)
------------------

* Feature - Added support for enumerating regions for `Aws::Proton`.

1.465.0 (2021-05-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.464.0 (2021-05-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.463.0 (2021-05-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.462.0 (2021-05-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.461.0 (2021-05-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.460.0 (2021-05-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.459.0 (2021-05-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.458.0 (2021-05-18)
------------------

* Feature - Added support for enumerating regions for `Aws::AppRunner`.

1.457.0 (2021-05-17)
------------------

* Feature - Added support for enumerating regions for `Aws::ApplicationCostProfiler`.

1.456.0 (2021-05-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.455.0 (2021-05-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.454.0 (2021-05-11)
------------------

* Feature - Added support for enumerating regions for `Aws::SSMIncidents`.

* Feature - Added support for enumerating regions for `Aws::SSMContacts`.

1.453.0 (2021-05-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.452.0 (2021-05-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.451.0 (2021-05-03)
------------------

* Feature - Added support for enumerating regions for `Aws::FinSpaceData`.

1.450.0 (2021-05-03)
------------------

* Feature - Added support for enumerating regions for `Aws::Finspace`.

1.449.0 (2021-04-28)
------------------

* Feature - Added support for enumerating regions for `Aws::NimbleStudio`.

1.448.0 (2021-04-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.447.0 (2021-04-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.446.0 (2021-04-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.445.0 (2021-04-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.444.0 (2021-04-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.443.0 (2021-04-08)
------------------

* Feature - Added support for enumerating regions for `Aws::LookoutEquipment`.

1.442.0 (2021-04-07)
------------------

* Feature - Added support for enumerating regions for `Aws::Mgn`.

1.441.0 (2021-04-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.440.0 (2021-04-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.439.0 (2021-03-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.438.0 (2021-03-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.437.0 (2021-03-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.436.0 (2021-03-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.435.0 (2021-03-25)
------------------

* Feature - Added support for enumerating regions for `Aws::LookoutMetrics`.

1.434.0 (2021-03-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.433.0 (2021-03-15)
------------------

* Feature - Added support for enumerating regions for `Aws::FIS`.

1.432.0 (2021-03-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.431.1 (2021-03-05)
------------------

* Issue - Fix an issue where services without regionalized endpoints do not resolve to a provided FIPS global region.

1.431.0 (2021-03-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

* Issue - Include LICENSE, CHANGELOG, and VERSION files with this gem.

1.430.0 (2021-03-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.


1.429.0 (2021-02-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.428.0 (2021-02-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.427.0 (2021-02-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.426.0 (2021-02-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

* Issue - Fix incorrect use of `JSON.load` breaking Ruby <= 2.2.

1.425.0 (2021-02-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

* Issue - Reduce memory usage by de-duplicating `partitions.json`.

1.424.0 (2021-02-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.423.0 (2021-02-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.422.0 (2021-01-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.421.0 (2021-01-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.420.0 (2021-01-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.419.0 (2021-01-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.418.0 (2021-01-22)
------------------

* Feature - Added support for enumerating regions for `Aws::LexRuntimeV2`.

* Feature - Added support for enumerating regions for `Aws::LexModelsV2`.

1.417.0 (2021-01-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.416.0 (2021-01-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.415.0 (2021-01-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.414.0 (2020-12-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.413.0 (2020-12-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.412.0 (2020-12-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.411.0 (2020-12-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.410.0 (2020-12-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.409.0 (2020-12-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.408.0 (2020-12-16)
------------------

* Feature - Added support for enumerating regions for `Aws::WellArchitected`.

* Feature - Added support for enumerating regions for `Aws::LocationService`.

1.407.0 (2020-12-15)
------------------

* Feature - Added support for enumerating regions for `Aws::PrometheusService`.

* Feature - Added support for enumerating regions for `Aws::IoTWireless`.

* Feature - Added support for enumerating regions for `Aws::IoTFleetHub`.

* Feature - Added support for enumerating regions for `Aws::IoTDeviceAdvisor`.

* Feature - Added support for enumerating regions for `Aws::GreengrassV2`.

1.406.0 (2020-12-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.405.0 (2020-12-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.404.0 (2020-12-08)
------------------

* Feature - Added support for enumerating regions for `Aws::SagemakerEdgeManager`.

* Feature - Added support for enumerating regions for `Aws::HealthLake`.

* Feature - Added support for enumerating regions for `Aws::EMRContainers`.

* Feature - Added support for enumerating regions for `Aws::AuditManager`.

1.403.0 (2020-12-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.402.0 (2020-12-02)
------------------

* Feature - Added support for enumerating regions for `Aws::CustomerProfiles`.

1.401.0 (2020-12-01)
------------------

* Feature - Added support for enumerating regions for `Aws::SageMakerFeatureStoreRuntime`.

* Feature - Added support for enumerating regions for `Aws::Profile`.

* Feature - Added support for enumerating regions for `Aws::LookoutforVision`.

* Feature - Added support for enumerating regions for `Aws::ECRPublic`.

* Feature - Added support for enumerating regions for `Aws::DevOpsGuru`.

* Feature - Added support for enumerating regions for `Aws::ConnectContactLens`.

* Feature - Added support for enumerating regions for `Aws::AppIntegrationsService`.

* Feature - Added support for enumerating regions for `Aws::AmplifyBackend`.

1.400.0 (2020-12-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.399.0 (2020-11-24)
------------------

* Feature - Added support for enumerating regions for `Aws::MWAA`.

1.398.0 (2020-11-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.397.0 (2020-11-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.396.0 (2020-11-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.395.0 (2020-11-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.394.0 (2020-11-17)
------------------

* Feature - Added support for enumerating regions for `Aws::NetworkFirewall`.

1.393.0 (2020-11-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.392.0 (2020-11-12)
------------------

* Feature - Added support for enumerating regions for `Aws::AppRegistry`.

1.391.0 (2020-11-11)
------------------

* Feature - Added support for enumerating regions for `Aws::GlueDataBrew`.

1.390.0 (2020-11-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.389.0 (2020-11-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.388.0 (2020-10-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.387.0 (2020-10-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.386.0 (2020-10-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.385.0 (2020-10-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.384.0 (2020-10-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.383.0 (2020-10-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.382.0 (2020-10-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.381.0 (2020-10-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.380.0 (2020-10-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.379.0 (2020-10-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.378.0 (2020-09-30)
------------------

* Feature - Added support for enumerating regions for `Aws::S3Outposts`.

1.377.0 (2020-09-29)
------------------

* Feature - Added support for enumerating regions for `Aws::TimestreamWrite`.

* Feature - Added support for enumerating regions for `Aws::TimestreamQuery`.

1.376.0 (2020-09-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.375.0 (2020-09-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.374.0 (2020-09-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.373.0 (2020-09-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.372.0 (2020-09-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.371.0 (2020-09-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.370.0 (2020-09-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.369.0 (2020-09-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.368.0 (2020-09-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.367.0 (2020-09-10)
------------------

* Feature - Added support for enumerating regions for `Aws::SSOAdmin`.

1.366.0 (2020-09-09)
------------------

* Feature - Added support for enumerating regions for `Aws::RedshiftDataAPIService`.

1.365.0 (2020-09-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.364.0 (2020-09-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.363.0 (2020-08-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.362.0 (2020-08-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.361.0 (2020-08-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.360.0 (2020-08-26)
------------------

* Feature - Added support for enumerating regions for `Aws::Appflow`.

1.359.0 (2020-08-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.358.0 (2020-08-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.357.0 (2020-08-18)
------------------

* Feature - Added support for enumerating regions for `Aws::IdentityStore`.

1.356.0 (2020-08-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.355.0 (2020-08-13)
------------------

* Feature - Added support for enumerating regions for `Aws::Braket`.

1.354.0 (2020-08-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.353.0 (2020-08-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.352.0 (2020-08-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.351.0 (2020-08-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.350.0 (2020-08-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.349.0 (2020-07-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.348.0 (2020-07-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.347.0 (2020-07-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.346.0 (2020-07-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.345.0 (2020-07-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.344.0 (2020-07-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.343.0 (2020-07-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.342.0 (2020-07-15)
------------------

* Feature - Added support for enumerating regions for `Aws::IVS`.

1.341.0 (2020-07-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.340.0 (2020-07-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.339.0 (2020-07-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.338.0 (2020-07-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.337.0 (2020-06-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.336.0 (2020-06-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.335.0 (2020-06-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.334.0 (2020-06-24)
------------------

* Feature - Added support for enumerating regions for `Aws::Honeycode`.

1.333.0 (2020-06-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.332.0 (2020-06-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.331.0 (2020-06-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.330.0 (2020-06-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.329.0 (2020-06-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

* Issue - Republish previous version with correct dependency on `aws-sdk-core`.

1.328.0 (2020-06-10)
------------------

* Issue - This version has been yanked. (#2327).
* Feature - Added support for enumerating regions for `Aws::CodeArtifact`.

1.327.0 (2020-06-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.326.0 (2020-06-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.325.0 (2020-06-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.324.0 (2020-06-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.323.0 (2020-06-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.322.0 (2020-05-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.321.0 (2020-05-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.320.0 (2020-05-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.319.0 (2020-05-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.318.0 (2020-05-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.317.0 (2020-05-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.316.0 (2020-05-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.315.0 (2020-05-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.314.0 (2020-05-13)
------------------

* Feature - Added support for enumerating regions for `Aws::Macie2`.

1.313.0 (2020-05-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.312.0 (2020-05-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.311.0 (2020-05-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.310.0 (2020-05-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.309.0 (2020-05-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.308.0 (2020-05-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.307.0 (2020-04-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.306.0 (2020-04-29)
------------------

* Feature - Added support for enumerating regions for `Aws::IoTSiteWise`.

1.305.0 (2020-04-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.304.0 (2020-04-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.303.0 (2020-04-24)
------------------

* Feature - Added support for enumerating regions for `Aws::ElasticInference`.

1.302.0 (2020-04-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.301.0 (2020-04-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.300.0 (2020-04-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.299.0 (2020-04-20)
------------------

* Feature - Added support for enumerating regions for `Aws::Synthetics`.

1.298.0 (2020-04-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.297.0 (2020-04-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.296.0 (2020-04-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.295.0 (2020-04-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.294.0 (2020-04-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.293.0 (2020-04-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.292.0 (2020-03-31)
------------------

* Feature - Added support for enumerating regions for `Aws::ElasticInference`.

1.291.0 (2020-03-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.290.0 (2020-03-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.289.0 (2020-03-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.288.0 (2020-03-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.287.0 (2020-03-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.286.0 (2020-03-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

* Issue - Change the default of sts_regional_endpoints from 'legacy' to 'regional'.

1.285.0 (2020-03-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.284.0 (2020-03-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.283.0 (2020-03-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.282.0 (2020-03-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.281.0 (2020-03-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.280.0 (2020-03-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.279.0 (2020-03-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.278.0 (2020-02-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.277.0 (2020-02-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.276.0 (2020-02-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.275.0 (2020-02-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.274.0 (2020-02-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.273.0 (2020-02-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.272.0 (2020-02-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.271.0 (2020-02-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.270.0 (2020-02-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.269.0 (2020-01-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.268.0 (2020-01-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.267.0 (2020-01-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.266.0 (2020-01-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.265.0 (2020-01-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.264.0 (2020-01-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.263.0 (2020-01-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.262.0 (2020-01-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.261.0 (2020-01-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.260.0 (2019-12-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.259.0 (2019-12-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.258.0 (2019-12-19)
------------------

* Feature - Added support for enumerating regions for `Aws::CodeStarconnections`.

1.257.0 (2019-12-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.256.0 (2019-12-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.255.0 (2019-12-13)
------------------

* Feature - Added support for enumerating regions for `Aws::Detective`.

1.254.0 (2019-12-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.253.0 (2019-12-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.252.0 (2019-12-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.251.0 (2019-12-05)
------------------

* Feature - Added support for enumerating regions for `Aws::KinesisVideoSignalingChannels`.

1.250.0 (2019-12-04)
------------------

* Feature - Added support for enumerating regions for `Aws::EBS`.

1.249.0 (2019-12-03)
------------------

* Feature - Added support for enumerating regions for `Aws::Outposts`.

* Feature - Added support for enumerating regions for `Aws::NetworkManager`.

* Feature - Added support for enumerating regions for `Aws::Kendra`.

* Feature - Added support for enumerating regions for `Aws::FraudDetector`.

* Feature - Added support for enumerating regions for `Aws::ComputeOptimizer`.

* Feature - Added support for enumerating regions for `Aws::CodeGuruReviewer`.

* Feature - Added support for enumerating regions for `Aws::CodeGuruProfiler`.

* Feature - Added support for enumerating regions for `Aws::AugmentedAIRuntime`.

1.248.0 (2019-12-02)
------------------

* Feature - Added support for enumerating regions for `Aws::AccessAnalyzer`.

1.247.0 (2019-12-02)
------------------

* Feature - Added support for enumerating regions for `Aws::Schemas`.

* Feature - Added support for enumerating regions for `Aws::Imagebuilder`.

1.246.0 (2019-11-26)
------------------

* Feature - Added support for enumerating regions for `Aws::ElasticInference`.

1.245.0 (2019-11-25)
------------------

* Feature - Added support for enumerating regions for `Aws::WAFV2`.

* Feature - Added support for enumerating regions for `Aws::IoTSecureTunneling`.

* Feature - Added support for enumerating regions for `Aws::AppConfig`.

1.244.0 (2019-11-22)
------------------

* Feature - Added support for enumerating regions for `Aws::AutoScalingPlans`.

* Feature - Added `Partition#region?` and `Partition#service?` methods.

1.243.0 (2019-11-21)
------------------

* Feature - Added support for enumerating regions for `Aws::ConnectParticipant`.


1.242.0 (2019-11-20)
------------------

* Feature - Added support for enumerating regions for `Aws::MigrationHubConfig`.

1.241.0 (2019-11-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.240.0 (2019-11-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.239.0 (2019-11-13)
------------------

* Feature - Added support for enumerating regions for `Aws::SESV2`.

* Feature - Added support for enumerating regions for `Aws::DataExchange`.

* Feature - Added support for S3 IAD regional endpoint.

1.238.0 (2019-11-12)
------------------

* Feature - Added support for enumerating regions for `Aws::MarketplaceCatalog`.

1.237.0 (2019-11-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.236.0 (2019-11-07)
------------------

* Feature - Added support for enumerating regions for `Aws::SSOOIDC`.

* Feature - Added support for enumerating regions for `Aws::SSO`.

1.235.0 (2019-11-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.234.0 (2019-11-06)
------------------

* Feature - Added support for enumerating regions for `Aws::SavingsPlans`.

1.233.0 (2019-11-05)
------------------

* Feature - Added support for enumerating regions for `Aws::CodeStarNotifications`.

1.232.0 (2019-10-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.231.0 (2019-10-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.230.0 (2019-10-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.229.0 (2019-10-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.228.0 (2019-10-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.227.0 (2019-10-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.226.0 (2019-10-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.225.0 (2019-10-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.224.0 (2019-10-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.223.0 (2019-10-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.222.0 (2019-10-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.221.0 (2019-10-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.220.0 (2019-09-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.219.0 (2019-09-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.218.0 (2019-09-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.217.0 (2019-09-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.216.0 (2019-09-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.215.0 (2019-09-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.214.0 (2019-09-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.213.0 (2019-09-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.212.0 (2019-09-12)
------------------

* Feature - Added support for enumerating regions for `Aws::WorkMailMessageFlow`.

1.211.0 (2019-09-09)
------------------

* Feature - Added support for enumerating regions for `Aws::QLDBSession`.

* Feature - Added support for enumerating regions for `Aws::QLDB`.

1.210.0 (2019-09-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.209.0 (2019-09-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.208.0 (2019-09-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.207.0 (2019-08-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.206.0 (2019-08-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.205.0 (2019-08-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.204.0 (2019-08-21)
------------------

* Feature - Added support for enumerating regions for `Aws::ForecastService`.

* Feature - Added support for enumerating regions for `Aws::ForecastQueryService`.

1.203.0 (2019-08-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.202.0 (2019-08-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.201.0 (2019-08-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.200.0 (2019-08-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.199.0 (2019-08-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.198.0 (2019-08-08)
------------------

* Feature - Added support for enumerating regions for `Aws::LakeFormation`.

1.197.0 (2019-08-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.196.0 (2019-08-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.195.0 (2019-07-30)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.194.0 (2019-07-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.193.0 (2019-07-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.192.0 (2019-07-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.191.0 (2019-07-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.190.0 (2019-07-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.189.0 (2019-07-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.188.0 (2019-07-11)
------------------

* Feature - Added support for enumerating regions for `Aws::EventBridge`.

1.187.0 (2019-07-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.186.0 (2019-07-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.185.0 (2019-07-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.184.0 (2019-07-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.183.0 (2019-07-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.182.0 (2019-06-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.181.0 (2019-06-27)
------------------

* Feature - Added support for enumerating regions for `Aws::EC2InstanceConnect`.

1.180.0 (2019-06-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.179.0 (2019-06-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.178.0 (2019-06-24)
------------------

* Feature - Added support for enumerating regions for `Aws::ServiceQuotas`.

* Feature - Added support for enumerating regions for `Aws::ApplicationInsights`.

1.177.0 (2019-06-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.176.0 (2019-06-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.175.0 (2019-06-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.174.0 (2019-06-10)
------------------

* Feature - Added support for enumerating regions for `Aws::PersonalizeRuntime`.

* Feature - Added support for enumerating regions for `Aws::PersonalizeEvents`.

* Feature - Added support for enumerating regions for `Aws::Personalize`.

1.173.0 (2019-06-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.172.0 (2019-06-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.171.0 (2019-06-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.170.0 (2019-05-30)
------------------

* Feature - Added support for enumerating regions for `Aws::IoTEventsData`.

* Feature - Added support for enumerating regions for `Aws::IoTEvents`.

1.169.0 (2019-05-29)
------------------

* Feature - Added support for enumerating regions for `Aws::IoTThingsGraph`.

1.168.0 (2019-05-28)
------------------

* Feature - Added support for enumerating regions for `Aws::GroundStation`.

1.167.0 (2019-05-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.166.0 (2019-05-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endpoints.

1.165.0 (2019-05-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.164.0 (2019-05-20)
------------------

* Feature - Added support for enumerating regions for `Aws::MediaPackageVod`.

1.163.0 (2019-05-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.162.0 (2019-05-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.161.0 (2019-05-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.160.0 (2019-05-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.159.0 (2019-05-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.158.0 (2019-05-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.157.0 (2019-05-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.156.0 (2019-04-30)
------------------

* Feature - Added support for enumerating regions for `Aws::ManagedBlockchain`.

1.155.0 (2019-04-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.154.0 (2019-04-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.153.0 (2019-04-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.152.0 (2019-04-24)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.151.0 (2019-04-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.150.0 (2019-04-16)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.149.0 (2019-04-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.148.0 (2019-03-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.147.0 (2019-03-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.146.0 (2019-03-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.145.0 (2019-03-21)
------------------

* Feature - Added support for enumerating regions for `Aws::TranscribeStreamingService`.

1.144.0 (2019-03-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.143.0 (2019-03-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.142.0 (2019-03-05)
------------------

* Feature - Added support for enumerating regions for `Aws::Textract`.

1.141.0 (2019-02-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.140.0 (2019-02-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.139.0 (2019-02-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.138.0 (2019-02-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.137.0 (2019-02-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.136.0 (2019-01-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.135.0 (2019-01-24)
------------------

* Feature - Added support for enumerating regions for `Aws::ECR`.

1.134.0 (2019-01-23)
------------------

* Feature - Added support for enumerating regions for `Aws::WorkLink`.

1.133.0 (2019-01-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.132.0 (2019-01-16)
------------------

* Feature - Added support for enumerating regions for `Aws::Backup`.

1.131.0 (2019-01-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.130.0 (2019-01-09)
------------------

* Feature - Added support for enumerating regions for `Aws::DocDB`.

1.129.0 (2019-01-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.128.0 (2019-01-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.127.0 (2018-12-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.126.0 (2018-12-18)
------------------

* Feature - Added support for enumerating regions for `Aws::ApiGatewayV2`.

* Feature - Added support for enumerating regions for `Aws::ApiGatewayManagementApi`.

1.125.0 (2018-12-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.124.0 (2018-12-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.123.0 (2018-12-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.122.0 (2018-12-04)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.121.0 (2018-11-29)
------------------

* Feature - Added support for enumerating regions for `Aws::Kafka`.

1.120.0 (2018-11-29)
------------------

* Feature - Added support for enumerating regions for `Aws::LicenseManager`.

* Feature - Added support for enumerating regions for `Aws::AppMesh`.

1.119.0 (2018-11-28)
------------------

* Feature - Added support for enumerating regions for `Aws::SecurityHub`.

* Feature - Added support for enumerating regions for `Aws::FSx`.

1.118.0 (2018-11-28)
------------------

* Feature - Added support for enumerating regions for `Aws::MediaConnect`.

* Feature - Added support for enumerating regions for `Aws::KinesisAnalyticsV2`.

* Feature - Added support for enumerating regions for `Aws::ComprehendMedical`.

1.117.0 (2018-11-27)
------------------

* Feature - Added support for enumerating regions for `Aws::GlobalAccelerator`.

1.116.0 (2018-11-26)
------------------

* Feature - Added support for enumerating regions for `Aws::Transfer`.

* Feature - Added support for enumerating regions for `Aws::RoboMaker`.

* Feature - Added support for enumerating regions for `Aws::DataSync`.

* Feature - Added support for enumerating regions for `Aws::Amplify`.

1.115.0 (2018-11-20)
------------------

* Feature - Added support for enumerating regions for `Aws::RDSDataService`.

* Feature - Added support for enumerating regions for `Aws::QuickSight`.

1.114.0 (2018-11-15)
------------------

* Feature - Added support for enumerating regions for `Aws::S3Control`.

* Feature - Added support for enumerating regions for `Aws::Route53Resolver`.

* Feature - Added support for enumerating regions for `Aws::RAM`.

* Feature - Added support for enumerating regions for `Aws::PinpointSMSVoice`.

1.113.0 (2018-11-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.112.0 (2018-11-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.111.0 (2018-11-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.110.0 (2018-11-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.109.0 (2018-11-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.108.0 (2018-11-06)
------------------

* Feature - Added support for enumerating regions for `Aws::PinpointEmail`.

1.107.0 (2018-10-30)
------------------

* Feature - Added support for enumerating regions for `Aws::Chime`.

1.106.0 (2018-10-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.105.0 (2018-09-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.104.0 (2018-09-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.103.0 (2018-09-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.102.0 (2018-08-27)
------------------

* Feature - Added support for enumerating regions for `Aws::Signer`.

1.101.0 (2018-08-27)
------------------

* Feature - Added support for enumerating regions for `Aws::signer`.

1.100.0 (2018-08-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.99.0 (2018-08-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.98.0 (2018-08-13)
------------------

* Feature - Added support for enumerating regions for `Aws::SageMaker`.

1.97.0 (2018-08-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.96.0 (2018-07-12)
------------------

* Feature - Added support for enumerating regions for `Aws::DLM`.

1.95.0 (2018-07-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.94.0 (2018-06-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.93.0 (2018-06-21)
------------------

* Feature - Added support for enumerating regions for `Aws::Macie`.

1.92.0 (2018-06-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.91.0 (2018-06-04)
------------------

* Feature - Added support for enumerating regions for `Aws::EKS`.

1.90.0 (2018-06-01)
------------------

* Feature - Added support for enumerating regions for `Aws::MediaTailor`.

1.89.1 (2018-05-31)
------------------

* Issue - Revert a few improperly configured endpoints.

1.89.0 (2018-05-30)
------------------

* Feature - Added support for enumerating regions for `Aws::Neptune`.

1.88.0 (2018-05-29)
------------------

* Feature - Added support for enumerating regions for `Aws::PI`.

1.87.0 (2018-05-14)
------------------

* Feature - Added support for enumerating regions for `Aws::IoT1ClickProjects`.

* Feature - Added support for enumerating regions for `Aws::IoT1ClickDevicesService`.

1.86.0 (2018-05-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.85.0 (2018-05-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.84.0 (2018-05-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.83.0 (2018-05-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.82.0 (2018-04-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.81.0 (2018-04-23)
------------------

* Feature - Added support for enumerating regions for `Aws::IoTAnalytics`.

1.80.0 (2018-04-10)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.79.0 (2018-04-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.78.0 (2018-04-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.77.0 (2018-04-04)
------------------

* Feature - Added support for enumerating regions for `Aws::SecretsManager`.

* Feature - Added support for enumerating regions for `Aws::FMS`.

* Feature - Added support for enumerating regions for `Aws::ACMPCA`.

1.76.0 (2018-03-30)
------------------

* Feature - Added support for enumerating regions for `Aws::Connect`.

1.75.0 (2018-03-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.74.0 (2018-03-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.73.0 (2018-03-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.72.0 (2018-03-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.71.0 (2018-03-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.70.0 (2018-03-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.69.0 (2018-03-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.68.0 (2018-02-28)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.67.0 (2018-02-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.66.0 (2018-02-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.65.0 (2018-02-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.64.0 (2018-02-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.63.0 (2018-02-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.62.0 (2018-02-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.61.0 (2018-02-13)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.60.0 (2018-02-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.59.0 (2018-02-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.58.0 (2018-02-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.57.0 (2018-01-25)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.56.0 (2018-01-19)
------------------

* Feature - Added support for enumerating regions for `Aws::TranscribeService`.

1.55.0 (2018-01-17)
------------------

* Feature - Added support for enumerating regions for `Aws::AutoScalingPlans`.

1.54.0 (2018-01-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.53.0 (2017-12-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.52.0 (2017-12-21)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.51.0 (2017-12-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.50.0 (2017-12-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.49.0 (2017-12-19)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.48.0 (2017-12-14)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.47.0 (2017-12-12)
------------------

* Feature - Added support for enumerating regions for `Aws::WorkMail`.

1.46.0 (2017-12-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.45.0 (2017-12-05)
------------------

* Feature - Added support for enumerating regions for `Aws::ServiceDiscovery`.

1.44.0 (2017-11-30)
------------------

* Feature - Added support for enumerating regions for `Aws::ServerlessApplicationRepository`.

* Feature - Added support for enumerating regions for `Aws::Cloud9`.

* Feature - Added support for enumerating regions for `Aws::AlexaForBusiness`.

1.43.0 (2017-11-30)
------------------

* Feature - Added support for enumerating regions for `Aws::ResourceGroups`.

1.42.0 (2017-11-29)
------------------

* Feature - Added support for enumerating regions for `Aws::Translate`.

* Feature - Added support for enumerating regions for `Aws::SageMakerRuntime`.

* Feature - Added support for enumerating regions for `Aws::SageMaker`.

* Feature - Added support for enumerating regions for `Aws::KinesisVideoMedia`.

* Feature - Added support for enumerating regions for `Aws::KinesisVideoArchivedMedia`.

* Feature - Added support for enumerating regions for `Aws::KinesisVideo`.

* Feature - Added support for enumerating regions for `Aws::IoTJobsDataPlane`.

* Feature - Added support for enumerating regions for `Aws::Comprehend`.

1.41.0 (2017-11-29)
------------------

* Feature - Added support for enumerating regions for `Aws::MQ`.

* Feature - Added support for enumerating regions for `Aws::GuardDuty`.

* Feature - Added support for enumerating regions for `Aws::AppSync`.

1.40.0 (2017-11-27)
------------------

* Feature - Added support for enumerating regions for `Aws::MediaStoreData`.

* Feature - Added support for enumerating regions for `Aws::MediaStore`.

* Feature - Added support for enumerating regions for `Aws::MediaPackage`.

* Feature - Added support for enumerating regions for `Aws::MediaLive`.

* Feature - Added support for enumerating regions for `Aws::MediaConvert`.

1.39.0 (2017-11-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.38.0 (2017-11-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.37.0 (2017-11-20)
------------------

* Feature - Added support for enumerating regions for `Aws::CostExplorer`.

1.36.0 (2017-11-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.35.0 (2017-11-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.34.0 (2017-11-09)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.33.0 (2017-11-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.32.0 (2017-11-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.31.0 (2017-11-07)
------------------

* Feature - Added support for enumerating regions for `Aws::Pricing`.

1.30.0 (2017-11-03)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.29.0 (2017-11-02)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.28.0 (2017-11-01)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.27.0 (2017-10-26)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

* Issue - Handle service identifier with empty value.

1.26.0 (2017-10-17)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.25.0 (2017-10-11)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.24.0 (2017-09-27)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.23.0 (2017-09-22)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.22.0 (2017-09-20)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.21.0 (2017-09-12)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.20.0 (2017-09-07)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.19.0 (2017-09-05)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.18.0 (2017-09-01)
------------------

* Feature - Added support for enumerating regions for `Aws::Mobile`.

1.17.0 (2017-08-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

* Issue - Update the `aws-partitions` gemspec metadata

1.16.0 (2017-08-23)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.15.0 (2017-08-18)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.14.0 (2017-08-15)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.13.0 (2017-08-14)
------------------

* Feature - Added support for enumerating regions for `Aws::MigrationHub`.

* Feature - Added support for enumerating regions for `Aws::Glue`.

* Feature - Added support for enumerating regions for `Aws::CloudHSMV2`.

1.12.0 (2017-07-31)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.11.0 (2017-07-06)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.10.0 (2017-06-29)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.9.0 (2017-06-26)
------------------

* Feature - Added support for enumerating regions for `Aws::Greengrass`.

* Feature - Added support for enumerating regions for `Aws::DAX`.

1.8.0 (2017-05-23)
------------------

* Feature - Added support for enumerating regions for `Aws::Athena`.

1.7.0 (2017-05-05)
------------------

* Feature - Added support for enumerating regions for `Aws::States`.

* Feature - Added support for enumerating regions for `Aws::MarketplaceEntitlementService`.

* Feature - Added support for enumerating regions for `Aws::Lex`.

1.6.0 (2017-04-21)
------------------

* Feature - Added support for enumerating regions for `Aws::ResourceGroupsTaggingAPI`.

* Feature - Added support for enumerating regions for `Aws::LexModelBuildingService`.

* Feature - Added support for enumerating regions for `Aws::CodeStar`.

1.5.0 (2017-03-09)
------------------

* Feature - Added support for enumerating regions for `Aws::WorkDocs`.

1.4.0 (2017-03-08)
------------------

* Feature - Updated the partitions source data the determines the AWS service regions and endoints.

1.3.0 (2017-03-07)
------------------

* Feature - Added support for enumerating regions for `Aws::Organizations`.

* Feature - Added support for enumerating regions for `Aws::MTurk`.

* Feature - Added support for enumerating regions for `Aws::LexRuntimeService`.

* Feature - Added support for enumerating regions for `Aws::CloudDirectory`.

1.2.0 (2017-01-24)
------------------

* Feature - Added support for enumerating regions for `Aws::Batch`.

1.1.0 (2016-12-09)
------------------

* Feature - Added support for enumerating regions for `Aws::XRay`.

* Feature - Added support for enumerating regions for `Aws::WAFRegional`.

* Feature - Added support for enumerating regions for `Aws::Shield`.

* Feature - Added support for enumerating regions for `Aws::SFN`.

* Feature - Added support for enumerating regions for `Aws::Rekognition`.

* Feature - Added support for enumerating regions for `Aws::Polly`.

* Feature - Added support for enumerating regions for `Aws::Pinpoint`.

* Feature - Added support for enumerating regions for `Aws::OpsWorksCM`.

* Feature - Added support for enumerating regions for `Aws::Lightsail`.

* Feature - Added support for enumerating regions for `Aws::Health`.

* Feature - Added support for enumerating regions for `Aws::CodeBuild`.

* Feature - Added support for enumerating regions for `Aws::AppStream`.

1.0.0 (2016-12-05)
------------------

* Feature - Initial release of the `aws-partitions` gem.

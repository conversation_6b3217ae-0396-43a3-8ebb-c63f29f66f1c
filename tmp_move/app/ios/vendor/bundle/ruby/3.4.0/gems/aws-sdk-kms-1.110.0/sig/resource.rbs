# WARNING ABOUT GENERATED CODE
#
# This file is generated. See the contributing guide for more information:
# https://github.com/aws/aws-sdk-ruby/blob/version-3/CONTRIBUTING.md
#
# WARNING ABOUT GENERATED CODE

module Aws
  module KMS
    # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/KMS/Resource.html
    class Resource
      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/KMS/Resource.html#initialize-instance_method
      def initialize: (
                        ?client: Client,
                        ?credentials: untyped,
                        ?region: String,
                        ?access_key_id: String,
                        ?account_id: String,
                        ?active_endpoint_cache: bool,
                        ?adaptive_retry_wait_to_fill: bool,
                        ?auth_scheme_preference: Array[String],
                        ?client_side_monitoring: bool,
                        ?client_side_monitoring_client_id: String,
                        ?client_side_monitoring_host: String,
                        ?client_side_monitoring_port: Integer,
                        ?client_side_monitoring_publisher: untyped,
                        ?convert_params: bool,
                        ?correct_clock_skew: bool,
                        ?defaults_mode: String,
                        ?disable_host_prefix_injection: bool,
                        ?disable_request_compression: bool,
                        ?endpoint: String,
                        ?endpoint_cache_max_entries: Integer,
                        ?endpoint_cache_max_threads: Integer,
                        ?endpoint_cache_poll_interval: Integer,
                        ?endpoint_discovery: bool,
                        ?ignore_configured_endpoint_urls: bool,
                        ?log_formatter: untyped,
                        ?log_level: Symbol,
                        ?logger: untyped,
                        ?max_attempts: Integer,
                        ?profile: String,
                        ?request_checksum_calculation: String,
                        ?request_min_compression_size_bytes: Integer,
                        ?response_checksum_validation: String,
                        ?retry_backoff: Proc,
                        ?retry_base_delay: Float,
                        ?retry_jitter: (:none | :equal | :full | ^(Integer) -> Integer),
                        ?retry_limit: Integer,
                        ?retry_max_delay: Integer,
                        ?retry_mode: ("legacy" | "standard" | "adaptive"),
                        ?sdk_ua_app_id: String,
                        ?secret_access_key: String,
                        ?session_token: String,
                        ?sigv4a_signing_region_set: Array[String],
                        ?simple_json: bool,
                        ?stub_responses: untyped,
                        ?telemetry_provider: Aws::Telemetry::TelemetryProviderBase,
                        ?token_provider: untyped,
                        ?use_dualstack_endpoint: bool,
                        ?use_fips_endpoint: bool,
                        ?validate_params: bool,
                        ?endpoint_provider: untyped,
                        ?http_proxy: String,
                        ?http_open_timeout: (Float | Integer),
                        ?http_read_timeout: (Float | Integer),
                        ?http_idle_timeout: (Float | Integer),
                        ?http_continue_timeout: (Float | Integer),
                        ?ssl_timeout: (Float | Integer | nil),
                        ?http_wire_trace: bool,
                        ?ssl_verify_peer: bool,
                        ?ssl_ca_bundle: String,
                        ?ssl_ca_directory: String,
                        ?ssl_ca_store: String,
                        ?on_chunk_received: Proc,
                        ?on_chunk_sent: Proc,
                        ?raise_response_errors: bool
                      ) -> void
                    | (?Hash[Symbol, untyped]) -> void

      def client: () -> Client


    end
  end
end

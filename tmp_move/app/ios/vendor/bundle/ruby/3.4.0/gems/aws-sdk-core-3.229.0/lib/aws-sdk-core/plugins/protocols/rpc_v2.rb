# frozen_string_literal: true

module Aws
  module Plugins
    module Protocols
      class RpcV2 < Seahorse::Client::Plugin

        option(:protocol, 'smithy-rpc-v2-cbor')

        handler(Aws::RpcV2::Handler)
        handler(Aws::RpcV2::ContentType<PERSON>and<PERSON>, priority: 30)
        handler(Aws::RpcV2::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, step: :sign)

      end
    end
  end
end

module Aws
  module Errors
    class NonSupportedRubyVersionError < RuntimeError
    end

    # The base class for all errors returned by an Amazon Web Service.
    # All ~400 level client errors and ~500 level server errors are raised
    # as service errors.  This indicates it was an error returned from the
    # service and not one generated by the client.
    class ServiceError < RuntimeError
      def initialize: (untyped context, String? message, ?untyped data) -> void

      attr_reader code: String

      attr_reader context: untyped

      attr_reader data: untyped

      attr_accessor self.code: String?
    end
  end
end

i---
RalwaysApply: true
---

# RS Plus – Working Rules for API, UI, and Deploy

These rules capture how this codebase is wired end‑to‑end so future edits stay consistent and working.

## App structure

- App entry point: [lib/app.dart](mdc:lib/app.dart). Home uses the original clean design via `HomePageClean`.
- Theme and shared UI utilities live in [lib/core/theme/app_theme.dart](mdc:lib/core/theme/app_theme.dart) and widgets like headers/search in [lib/widgets/app_page_scaffold.dart](mdc:lib/widgets/app_page_scaffold.dart).

## API configuration and models

- API config lives in [lib/core/config/api_config.dart](mdc:lib/core/config/api_config.dart).
  - `ApiConfig.baseUrl` + `apiPath` build the base.
  - `getMediaUrl(relative)` must be used to resolve relative media URLs (e.g., `/api/media/file/...`).
- API models live in [lib/models/api_models.dart](mdc:lib/models/api_models.dart).
  - Always coerce ids/titles/strings (numbers can appear in Payload responses).
  - `SlideResponse.content` falls back to `description` when `content` is absent.
  - `MediaResponse.fullUrl` prefers `sizes.card.url` → `sizes.thumbnail.url` → `url`.
  - Relation fields (e.g., `image`, `moduleThumbnail`, `courseThumbnail`) may be an int/string id or an expanded object. Only construct `MediaResponse` when the value is a `Map<String, dynamic>`.

## API calling conventions

- Service: [lib/services/api_service.dart](mdc:lib/services/api_service.dart)
  - Authentication is handled automatically via `_ensureAuthToken`; replace test credentials before production.
  - Courses: `getCourses(limit, depth)` returns a paginated list.
  - Course detail: `getCourseDetail(courseId, depth)` expands modules.
  - Module detail and slides:
    - Do NOT rely on course/module depth to fully expand nested `slides.image`.
    - ALWAYS fetch slides via the bulk endpoint using `getSlides(slideIds, depth: 1)`.
    - `getSlides` accepts `List<dynamic>` and toStrings ids before joining to avoid type errors.
  - On HTTP 401, the service logs and returns a friendly error; callers should surface login prompts if needed.

## UI design rules (Original design everywhere)

- Home: use the original, clean card design with header + search ([lib/screens/home_page_clean.dart](mdc:lib/screens/home_page_clean.dart)).
  - Fetch courses from the API; only fall back to mock data on API failure.
- Course Detail (API): use the original course detail layout ([lib/screens/course_detail_page_api.dart](mdc:lib/screens/course_detail_page_api.dart))
  - Header via `AppSliverHeader`.
  - Map API `ModuleResponse` → legacy `Module` only for rendering `ModuleCard` ([lib/widgets/module_card.dart](mdc:lib/widgets/module_card.dart)).
  - Tapping a module navigates to the slides viewer.
- Module Slides: use the original, swipeable card design ([lib/screens/module_slides_swiper.dart](mdc:lib/screens/module_slides_swiper.dart)).
  - Front shows full‑bleed image, title badge as overlay; tap flips to text details.
  - Progress bar and animated nav are included.
  - If a slide has no image, show the graceful placeholder.

## Rendering and performance

- Prefer `withOpacity()` over `withAlpha()` for color adjustments to avoid Impeller validation warnings.
- When animating or applying opacity, don’t wrap primitives that cannot accept inherited opacity.

## Known Payload CMS behaviors to handle

- Depth expansion is shallow: course `modules` might expand, but module `slides.image` may still be IDs.
- Media URLs are often relative. Always resolve via `ApiConfig.getMediaUrl` and prefer `sizes.card`.
- IDs may be numeric; always coerce to strings at the edges (model parsing & query building).

## CLI/API diagnostic helpers

- Python helper script for API checks: [test_api.py](mdc:test_api.py).
  - Useful to inspect a slide/module response and confirm `image` object presence and URLs.

## iOS build & delivery (App Store Connect API key – recommended)

This repo uses the passwordless App Store Connect API key flow (no interactive prompts):

1) One‑time setup
- Put your `.p8` key at: `~/.appstoreconnect/private_keys/AuthKey_<KEY_ID>.p8` (600 perms)
- Keep your `KEY_ID` and `ISSUER_ID` handy. Do not commit the key file.

2) Build the IPA
- `flutter build ipa --release --build-number <N> --export-options-plist=ios/ExportOptions.plist`
- Minimal export plist lives at [ios/ExportOptions.plist](mdc:ios/ExportOptions.plist)

3) Upload to TestFlight
- `xcrun altool --upload-app --type ios -f build/ios/ipa/*.ipa --apiKey <KEY_ID> --apiIssuer <ISSUER_ID>`
- Watch App Store Connect → TestFlight for processing to finish.

Notes
- Ensure git is clean before a release build (helps reproducibility).
- Prefer this API key flow over Apple ID/password to avoid interactive prompts.
- Fastlane is optional; if used, run via Bundler and keep the repo clean first. See [ios/fastlane/Fastfile](mdc:ios/fastlane/Fastfile).

## Do/Don't quick list

- Do: always bulk‑fetch slides after module detail to guarantee expanded images.
- Do: use `sizes.card` (or `thumbnail`) before original media URL for better device sizing.
- Do: keep Home/Course/Module screens aligned with the original unified design components.
- Don't: hardcode mock IDs like "1"; always pass through server IDs.
- Don't: use `withAlpha` where `withOpacity` suffices; it can trigger Impeller errors.

## CRITICAL: Development Rules for AI Assistants

### NEVER Run the App
- **DO NOT** execute `flutter run` or any commands that launch the application
- The developer runs the app in a separate terminal
- Focus only on code changes and static analysis

### ALWAYS Run Flutter Analyze
- **MANDATORY**: Run `flutter analyze` before declaring any task complete
- Check for all syntax errors, type mismatches, and linting issues
- Fix all issues before considering work done
- Run `flutter analyze` again after fixes to confirm

### Current Backend: Supabase (Not Payload)
- The app has been fully migrated from Payload API to Supabase
- Use `SupabaseRsService` for all API calls (located at `lib/services/supabase_rs_service.dart`)
- Images are stored in Supabase Storage bucket "Media"
- Progress tracking uses local SharedPreferences via `ProgressService`


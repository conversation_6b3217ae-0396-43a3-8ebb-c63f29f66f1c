import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/services.dart';
import 'package:skilldeck/core/theme/app_theme.dart';
import 'package:skilldeck/models/api_models.dart';
import 'package:skilldeck/services/progress_service.dart';
import 'package:palette_generator/palette_generator.dart';
import 'package:skilldeck/services/user_engagement_service.dart';

/// Experimental reels-style viewer: full-screen background image with segmented
/// progress bar on top. Built for the `reels-style` branch.
class ModuleReelsViewer extends StatefulWidget {
  final String courseId;
  final ModuleResponse module;
  final List<SlideResponse> slides;

  const ModuleReelsViewer({
    super.key,
    required this.courseId,
    required this.module,
    required this.slides,
  });

  @override
  State<ModuleReelsViewer> createState() => _ModuleReelsViewerState();
}

class _ModuleReelsViewerState extends State<ModuleReelsViewer>
    with TickerProviderStateMixin {
  late final PageController _pageController;
  late final AnimationController _autoController;
  final ProgressService _progressService = ProgressService();
  final UserEngagementService _engagement = UserEngagementService();

  // Progress state
  int _current = 0;
  Set<String> _completed = {};
  bool _paused = false;
  Color? _dominantColor;
  Set<String> _liked = {};
  Set<String> _saved = {};
  final Map<String, Color> _paletteCache = {};
  bool _showSwipeHint = true;

  static const Duration _perSlide = Duration(seconds: 5);

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _autoController = AnimationController(vsync: this, duration: _perSlide);
    _loadProgress();

    // Small image cache bump for snappier swipes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        PaintingBinding.instance.imageCache.maximumSize = 300; // default ~100
        PaintingBinding.instance.imageCache.maximumSizeBytes =
            150 * 1024 * 1024; // ~150MB
      } catch (_) {}
      Future<void>.delayed(const Duration(milliseconds: 2200), () {
        if (mounted) setState(() => _showSwipeHint = false);
      });
    });
  }

  Future<void> _loadProgress() async {
    try {
      final moduleProgress = await _progressService.getModuleProgress(
        widget.courseId,
        widget.module.id,
      );
      if (!mounted) return;

      final saveIndex = moduleProgress.currentCardIndex;
      final validIndex = saveIndex > 0 && saveIndex < widget.slides.length
          ? saveIndex
          : 0;

      final validCompleted = moduleProgress.completedCardIds
          .where((id) => widget.slides.any((s) => s.id == id))
          .toSet();

      setState(() {
        _current = validIndex;
        _completed = validCompleted;
      });

      // Jump to saved page then start animation
      await Future<void>.delayed(Duration.zero);
      _pageController.jumpToPage(_current);
      // Extract initial color
      _extractDominantColor(widget.slides[_current]);
      // Load engagement state
      _liked = await _engagement.getLiked();
      _saved = await _engagement.getSaved();
      if (mounted) setState(() {});
    } catch (_) {
      // Auto-advance disabled
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _autoController.dispose();
    super.dispose();
  }

  // Auto-advance disabled (kept for potential future use)

  void _pauseAuto() {
    if (!_paused) {
      _autoController.stop();
      setState(() => _paused = true);
    }
  }

  void _resumeAuto() {
    if (_paused) {
      _autoController.forward();
      setState(() => _paused = false);
    }
  }

  Future<void> _goNext() async {
    if (_current < widget.slides.length - 1) {
      HapticFeedback.selectionClick();
      _pageController.nextPage(
        duration: const Duration(milliseconds: 180),
        curve: Curves.easeOut,
      );
    } else {
      // End reached
      _autoController.stop();
    }
  }

  Future<void> _goPrev() async {
    if (_current > 0) {
      HapticFeedback.selectionClick();
      _pageController.previousPage(
        duration: const Duration(milliseconds: 180),
        curve: Curves.easeOut,
      );
    }
  }

  Future<void> _onChanged(int index) async {
    if (!mounted) return;
    setState(() => _current = index);
    // Auto-advance disabled

    // Persist position and mark viewed (non-blocking)
    final slide = widget.slides[index];
    _progressService
        .updateCurrentCardIndex(
          courseId: widget.courseId,
          moduleId: widget.module.id,
          cardIndex: index,
        )
        .catchError((_) {});
    if (!_completed.contains(slide.id)) {
      setState(() => _completed.add(slide.id));
      _progressService
          .markSlideComplete(
            courseId: widget.courseId,
            moduleId: widget.module.id,
            slideId: slide.id,
          )
          .catchError((_) {});
    }

    // Update dominant color for background tint
    _extractDominantColor(slide);
    // Prefetch neighbors to speed up swipes
    _prefetchAround(index);
  }

  Future<void> _extractDominantColor(SlideResponse slide) async {
    final url = slide.image?.originalFullUrl.isNotEmpty == true
        ? slide.image!.originalFullUrl
        : (slide.image?.fullUrl ?? '');
    if (url.isEmpty) return;
    if (_paletteCache.containsKey(url)) {
      setState(() => _dominantColor = _paletteCache[url]);
      return;
    }
    // Compute asynchronously to avoid blocking swipes
    Future.microtask(() async {
      try {
        final palette = await PaletteGenerator.fromImageProvider(
          NetworkImage(url),
          maximumColorCount: 8,
        );
        final color = (palette.dominantColor?.color ?? Colors.black)
            .withValues(alpha: 0.85);
        if (!mounted) return;
        _paletteCache[url] = color;
        setState(() => _dominantColor = color);
      } catch (_) {}
    });
  }

  void _prefetchAround(int index) {
    void prefetchFor(int i) {
      if (i < 0 || i >= widget.slides.length) return;
      final s = widget.slides[i];
      final url = s.image?.originalFullUrl.isNotEmpty == true
          ? s.image!.originalFullUrl
          : (s.image?.fullUrl ?? '');
      if (url.isEmpty) return;
      precacheImage(NetworkImage(url), context);
    }
    prefetchFor(index + 1);
    prefetchFor(index - 1);
  }

  @override
  Widget build(BuildContext context) {
    final slides = widget.slides;
    final theme = Theme.of(context);
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onLongPressStart: (_) => _pauseAuto(),
      onLongPressEnd: (_) => _resumeAuto(),
      child: Scaffold(
        backgroundColor: theme.colorScheme.surfaceContainerLowest,
        body: Stack(
          children: [
            // Pages
            NotificationListener<ScrollNotification>(
              onNotification: (notification) {
                // Auto-advance disabled; keep listener for extensibility
                return false;
              },
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: _onChanged,
                itemCount: slides.length,
                itemBuilder: (context, i) => _ParallaxReelSlide(
                  slide: slides[i],
                  controller: _pageController,
                  index: i,
                ),
              ),
            ),

            // Soft tinted overlays (top/bottom) using dominant color
            Positioned.fill(
              child: IgnorePointer(
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        (_dominantColor ?? theme.colorScheme.scrim).withValues(alpha: 0.45),
                        Colors.transparent,
                        (_dominantColor ?? theme.colorScheme.scrim).withValues(alpha: 0.60),
                      ],
                      stops: const [0.0, 0.55, 1.0],
                    ),
                  ),
                ),
              ),
            ),

            // Top progress and header with counter
            SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const _HazardDashes(),
                    const SizedBox(height: 6),
                    Row(
                      children: [
                        Expanded(
                          child: _SegmentedProgress(
                            length: slides.length,
                            current: _current,
                            controller: _autoController,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${_current + 1}/${slides.length}',
                          style: TextStyle(color: theme.colorScheme.onPrimary.withValues(alpha: 0.9), fontWeight: FontWeight.w600),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: 36,
                      height: 28,
                      child: IconButton(
                        icon: Icon(Icons.close, color: theme.colorScheme.onPrimary.withValues(alpha: 0.85), size: 12),
                        onPressed: () => Navigator.of(context).pop(),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(minWidth: 28, minHeight: 28),
                      ),
                    ),
                    const SizedBox(height: 6),
                    _SlideTitleChip(title: slides[_current].title, type: slides[_current].type),
                  ],
                ),
              ),
            ),

            // Tap zones like stories
            Positioned.fill(
              child: Row(
                children: [
                  Expanded(
                    child: GestureDetector(onTap: _goPrev),
                  ),
                  Expanded(
                    child: GestureDetector(onTap: _goNext),
                  ),
                ],
              ),
            ),

            // Swipe down anywhere near top to dismiss
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: 80,
              child: GestureDetector(
                onVerticalDragEnd: (details) {
                  if (details.primaryVelocity != null && details.primaryVelocity! > 300) {
                    Navigator.of(context).pop();
                  }
                },
                behavior: HitTestBehavior.translucent,
              ),
            ),

            // Bottom action bar (like/save)
            Positioned(
              left: 12,
              right: 12,
              bottom: MediaQuery.of(context).padding.bottom - 2,
              child: _BottomActions(
                slideId: slides[_current].id,
                liked: _liked.contains(slides[_current].id),
                saved: _saved.contains(slides[_current].id),
                onToggleLike: () async {
                  final updated = await _engagement.toggleLike(slides[_current].id);
                  if (mounted) setState(() => _liked = updated);
                  if (!mounted) return;
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('Added to Likes'),
                      behavior: SnackBarBehavior.floating,
                      backgroundColor: theme.colorScheme.scrim.withValues(alpha: 0.87),
                      action: SnackBarAction(
                        label: 'Undo',
                        onPressed: () async {
                          final undo = await _engagement.toggleLike(slides[_current].id);
                          if (mounted) setState(() => _liked = undo);
                        },
                        textColor: theme.colorScheme.onPrimary,
                      ),
                    ),
                  );
                },
                onToggleSave: () async {
                  final updated = await _engagement.toggleSave(slides[_current].id);
                  if (mounted) setState(() => _saved = updated);
                  if (!mounted) return;
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('Saved for later'),
                      behavior: SnackBarBehavior.floating,
                      backgroundColor: theme.colorScheme.scrim.withValues(alpha: 0.87),
                      action: SnackBarAction(
                        label: 'Undo',
                        onPressed: () async {
                          final undo = await _engagement.toggleSave(slides[_current].id);
                          if (mounted) setState(() => _saved = undo);
                        },
                        textColor: theme.colorScheme.onPrimary,
                      ),
                    ),
                  );
                },
              ),
            ),

            // First-slide swipe hint
            if (_current == 0)
              Positioned(
                bottom: MediaQuery.of(context).padding.bottom + 60,
                left: 0,
                right: 0,
                child: AnimatedOpacity(
                  duration: const Duration(milliseconds: 400),
                  opacity: _showSwipeHint ? 1 : 0,
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.scrim.withValues(alpha: 0.35),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: theme.colorScheme.onPrimary.withValues(alpha: 0.15)),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.swipe, color: theme.colorScheme.onPrimary.withValues(alpha: 0.7), size: 16),
                          SizedBox(width: 6),
                          Text('Swipe to continue', style: TextStyle(color: theme.colorScheme.onPrimary.withValues(alpha: 0.7))),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _ParallaxReelSlide extends StatelessWidget {
  final SlideResponse slide;
  final PageController controller;
  final int index;
  const _ParallaxReelSlide({required this.slide, required this.controller, required this.index});

  @override
  Widget build(BuildContext context) {
    final url = slide.image?.originalFullUrl.isNotEmpty == true
        ? slide.image!.originalFullUrl
        : (slide.image?.fullUrl ?? '');

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        double delta = 0;
        if (controller.hasClients) {
          final page = controller.page ?? controller.initialPage.toDouble();
          delta = (page - index).clamp(-1.0, 1.0);
        }
        final bgTranslate = 12 * delta; // subtle parallax for background

        return Stack(
          fit: StackFit.expand,
          children: [
            Transform.translate(
              offset: Offset(bgTranslate, 0),
              child: Container(color: theme.colorScheme.surfaceContainerLowest),
            ),
            if (url.isNotEmpty)
              Transform.translate(
                offset: Offset(delta * 6, 0),
                child: Image.network(
                  url,
                  fit: BoxFit.contain,
                  width: double.infinity,
                  height: double.infinity,
                ),
              )
            else
              Center(child: Icon(Icons.image_not_supported, color: theme.colorScheme.onSurface.withValues(alpha: 0.38), size: 64)),
          ],
        );
      },
    );
  }
}

class _BottomActions extends StatelessWidget {
  final String slideId;
  final bool liked;
  final bool saved;
  final VoidCallback onToggleLike;
  final VoidCallback onToggleSave;

  const _BottomActions({
    required this.slideId,
    required this.liked,
    required this.saved,
    required this.onToggleLike,
    required this.onToggleSave,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.35),
          borderRadius: BorderRadius.circular(28),
          border: Border.all(color: Colors.white.withValues(alpha: 0.15)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.25),
              blurRadius: 10,
              offset: const Offset(0, 4),
            )
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _pillIcon(
              icon: liked ? Icons.favorite : Icons.favorite_border,
              label: 'Like',
              color: liked ? AppColors.safetyOrange : Colors.white,
              onTap: onToggleLike,
            ),
            const SizedBox(width: 10),
            _pillIcon(
              icon: saved ? Icons.bookmark : Icons.bookmark_border,
              label: 'Save',
              color: saved ? AppColors.highVisYellow : Colors.white,
              onTap: onToggleSave,
            ),
            const SizedBox(width: 10),
            _pillIcon(
              icon: Icons.more_horiz,
              label: 'Advanced',
              color: Colors.white,
              onTap: () {},
              subtle: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _pillIcon({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
    bool subtle = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: subtle ? 0.25 : 0.4),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white.withValues(alpha: 0.15)),
            ),
            child: Icon(icon, size: 18, color: color),
          ),
          const SizedBox(width: 6),
          if (!subtle)
            Text(label, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }
}

class _SegmentedProgress extends StatelessWidget {
  final int length;
  final int current;
  final Animation<double> controller;
  const _SegmentedProgress({
    required this.length,
    required this.current,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Row(
          children: List.generate(length, (i) {
            final isPast = i < current;
            final isCurrent = i == current;
            final fill = isPast ? 1.0 : isCurrent ? controller.value : 0.0;
            return Expanded(
              child: Container(
                height: 4.0,
                margin: EdgeInsets.only(
                  right: i == length - 1 ? 0 : 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.25),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: FractionallySizedBox(
                    widthFactor: fill.clamp(0.0, 1.0),
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppColors.safetyOrange,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }
}

class _HazardDashes extends StatelessWidget {
  const _HazardDashes();
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 6,
      child: Row(
        children: List.generate(18, (i) {
          final isStripe = i % 2 == 0;
          return Expanded(
            child: Container(
              margin: EdgeInsets.only(right: i == 17 ? 0 : 2),
              decoration: BoxDecoration(
                color: isStripe ? AppColors.safetyOrange.withValues(alpha: 0.7) : Colors.white.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        }),
      ),
    );
  }
}

class _SlideTitleChip extends StatelessWidget {
  final String title;
  final String? type;
  const _SlideTitleChip({required this.title, required this.type});

  @override
  Widget build(BuildContext context) {
    final label = (type ?? '').isEmpty ? 'Slide' : type!.substring(0, 1).toUpperCase() + type!.substring(1);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.35),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.15)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.label_outline, size: 12, color: Colors.white.withValues(alpha: 0.85)),
          const SizedBox(width: 6),
          ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 260),
            child: Text(
              '$label • $title',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }
}



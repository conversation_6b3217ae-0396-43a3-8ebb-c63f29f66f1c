import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:skilldeck/models/api_models.dart';
// import 'package:skilldeck/core/theme/app_theme.dart';
import 'package:skilldeck/services/progress_service.dart';
import 'package:skilldeck/widgets/progress_tracker.dart';

class ModuleSlidesSwiper extends StatefulWidget {
  final String courseId;
  final ModuleResponse module;
  final List<SlideResponse> slides;
  final int? initialIndex;

  const ModuleSlidesSwiper({
    super.key,
    required this.courseId,
    required this.module,
    required this.slides,
    this.initialIndex,
  });

  @override
  State<ModuleSlidesSwiper> createState() => _ModuleSlidesSwiperState();
}

class _ModuleSlidesSwiperState extends State<ModuleSlidesSwiper>
    with TickerProviderStateMixin {
  late SwiperController _swiperController;
  late Animation<PERSON>ontroller _progressController;
  late AnimationController _cardFlipController;
  late AnimationController _swipeAnimationController;
  late AnimationController _momentumController;
  final ProgressService _progressService = ProgressService();

  int currentIndex = 0;
  bool showBack = false;
  Set<int> viewedCards = {};
  Set<String> completedSlides = {};

  // Enhanced swipe state (legacy)
  bool _hasPreloadedInitialImages = false;
  
  @override
  void initState() {
    super.initState();
    _swiperController = SwiperController();
    _progressController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _cardFlipController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _swipeAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _momentumController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    currentIndex = (widget.initialIndex ?? 0).clamp(0, widget.slides.isEmpty ? 0 : widget.slides.length - 1);
    viewedCards.add(currentIndex);
    _progressController.forward();
    
    // Debug logging for search navigation
    if (widget.initialIndex != null) {
      debugPrint('[MODULE SWIPER] Starting at initialIndex: ${widget.initialIndex}');
      debugPrint('[MODULE SWIPER] Clamped to currentIndex: $currentIndex');
      debugPrint('[MODULE SWIPER] Target slide ID: ${widget.slides[currentIndex].id}');
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        _swiperController.move(currentIndex, animation: false);
      } catch (_) {}
    });
    _loadProgress();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Preload images after the widget is fully initialized and MediaQuery is available
    if (!_hasPreloadedInitialImages) {
      _hasPreloadedInitialImages = true;
      _preloadImages();
    }
  }

  // Preload images to prevent loading delays during swipes
  void _preloadImages() {
    if (!mounted) return;

    // Preload the first 3 images to ensure smooth initial experience
    for (int i = 0; i < widget.slides.length && i < 3; i++) {
      final slide = widget.slides[i];
      final url = (slide.image?.originalFullUrl.isNotEmpty ?? false)
          ? slide.image!.originalFullUrl
          : (slide.image?.fullUrl ?? "");

      if (url.isNotEmpty) {
        // Use both precaching and direct caching for better performance
        precacheImage(NetworkImage(url), context);
        // Also cache with a key for faster retrieval
        NetworkImage(url).resolve(const ImageConfiguration());
      }
    }
  }

  // Preload next images when user swipes
  void _preloadNextImages(int currentIndex) {
    if (!mounted) return;

    // Preload next 2 images for smooth swiping
    for (int i = currentIndex + 1; i < widget.slides.length && i <= currentIndex + 2; i++) {
      final slide = widget.slides[i];
      final url = (slide.image?.originalFullUrl.isNotEmpty ?? false)
          ? slide.image!.originalFullUrl
          : (slide.image?.fullUrl ?? "");

      if (url.isNotEmpty) {
        // Use both precaching and direct caching for better performance
        precacheImage(NetworkImage(url), context);
        // Also cache with a key for faster retrieval
        NetworkImage(url).resolve(const ImageConfiguration());
      }
    }
  }

  Future<void> _loadProgress() async {
    try {
      final moduleProgress = await _progressService.getModuleProgress(
        widget.courseId,
        widget.module.id,
      );
      
      if (mounted) {
        // Filter completed slides to only include ones that exist in current slides
        final validCompletedSlides = moduleProgress.completedCardIds
            .where((slideId) => widget.slides.any((slide) => slide.id == slideId))
            .toSet();
        
        setState(() {
          completedSlides = validCompletedSlides;
          // Only resume from last position if no specific initialIndex was provided
          if (widget.initialIndex == null) {
            final savedIndex = moduleProgress.currentCardIndex;
            if (savedIndex > 0 && savedIndex < widget.slides.length) {
              currentIndex = savedIndex;
              _swiperController.move(currentIndex);
            }
          }
        });
        
        // Debug logging
        debugPrint('[PROGRESS] Loaded progress: ${validCompletedSlides.length}/${widget.slides.length}');
        debugPrint('[PROGRESS] Completed slide IDs: $validCompletedSlides');
        debugPrint('[PROGRESS] Available slide IDs: ${widget.slides.map((s) => s.id).toList()}');
      }
    } catch (e) {
      debugPrint('[PROGRESS] Error loading progress: $e');
    }
  }

  @override
  void dispose() {
    _swiperController.dispose();
    _progressController.dispose();
    _cardFlipController.dispose();
    _swipeAnimationController.dispose();
    _momentumController.dispose();
    super.dispose();
  }

  // Deprecated gesture handlers removed; Swiper handles gestures

  // Flipping disabled per request

  void _onSwipe(int index) async {
    if (!mounted) return;

    // Enhanced haptic feedback for better user experience
    HapticFeedback.mediumImpact();

    final wasAlreadyViewed = viewedCards.contains(index);

    setState(() {
      currentIndex = index;
      viewedCards.add(index);
      showBack = false;
    });

    // Enhanced animations with smoother transitions
    _cardFlipController.reset();
    _swipeAnimationController.forward();
    _progressController.forward();

    // Preload next images for smooth experience
    _preloadNextImages(index);
    
    try {
      // Save current position
      await _progressService.updateCurrentCardIndex(
        courseId: widget.courseId,
        moduleId: widget.module.id,
        cardIndex: index,
      );
      
      // Mark slide as completed only when first viewed (forward progress)
      if (index < widget.slides.length && !wasAlreadyViewed) {
        final slide = widget.slides[index];
        final slideId = slide.id;
        
        debugPrint('[PROGRESS] First time viewing slide $index: $slideId');
        
        if (!completedSlides.contains(slideId)) {
          if (mounted) {
            setState(() {
              completedSlides.add(slideId);
            });
          }
          
          await _progressService.markSlideComplete(
            courseId: widget.courseId,
            moduleId: widget.module.id,
            slideId: slideId,
          );
          
          // Debug logging
          final completedCount = widget.slides.where((s) => completedSlides.contains(s.id)).length;
          debugPrint('[PROGRESS] Slide completed: $slideId');
          debugPrint('[PROGRESS] Total completed: $completedCount/${widget.slides.length}');
          debugPrint('[PROGRESS] Progress: ${(completedCount / widget.slides.length * 100).round()}%');
          
          // Provide completion feedback
          HapticFeedback.lightImpact();
        }
      } else if (wasAlreadyViewed) {
        debugPrint('[PROGRESS] Returning to previously viewed slide $index');
      }
    } catch (e) {
      debugPrint('Error updating progress: $e');
    }
  }

  void _completeModule() {
    HapticFeedback.mediumImpact();
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Module completed! Great job! 🎉'),
        backgroundColor: Theme.of(context).colorScheme.tertiary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _showZoomedImage(String imageUrl) {
    HapticFeedback.lightImpact();
    showDialog(
      context: context,
      barrierColor: Theme.of(context).colorScheme.scrim.withOpacity(0.75),
      builder: (context) => Dialog.fullscreen(
        backgroundColor: Theme.of(context).colorScheme.surface,
        child: Stack(
          children: [
            Center(
              child: InteractiveViewer(
                panEnabled: true,
                scaleEnabled: true,
                minScale: 0.5,
                maxScale: 4.0,
                child: Image.network(
                  imageUrl,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return Center(
                      child: Icon(
                        Icons.image_not_supported,
                        size: 64,
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                      ),
                    );
                  },
                ),
              ),
            ),
            Positioned(
              top: 50,
              right: 20,
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.close,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: SafeArea(
          left: false,
          right: false,
          child: Stack(
            children: [
              // Background pattern
              Positioned.fill(
                child: CustomPaint(
                  painter: _BackgroundPatternPainter(
                    patternColor: theme.colorScheme.onSurface.withOpacity(isDark ? 0.06 : 0.06),
                  ),
                ),
              ),
              
              Column(
                children: [
                  // Header
                  _buildHeader(),
                  
                  // Progress Bar
                  _buildProgressBar(),
                  
                  // Cards with proper padding
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          final cardWidth = constraints.maxWidth; // Use available width with padding
                          final cardHeight = constraints.maxHeight * 0.88; // Slightly shorter for better spacing
                          return Swiper(
                            controller: _swiperController,
                            itemCount: widget.slides.length,
                            onIndexChanged: _onSwipe,
                            index: currentIndex,
                            loop: false,
                            layout: SwiperLayout.DEFAULT,
                            // Enhanced swipe settings with proper spacing
                            duration: 200, // Smooth transition
                            curve: Curves.easeOutCubic, // More natural easing
                            viewportFraction: 0.95, // Add small side margins for better visual separation
                            scale: 0.98, // Slight scale for depth
                            itemWidth: cardWidth,
                            itemHeight: cardHeight,
                            // Enhanced physics for momentum scrolling
                            physics: const BouncingScrollPhysics(
                              parent: const AlwaysScrollableScrollPhysics(),
                            ),
                            // Enhanced transformer for smooth transitions without blanking
                            transformer: null, // Use default transformer for smoother transitions
                            itemBuilder: (context, index) {
                              return _buildEnhancedSwipeCard(widget.slides[index], cardWidth, cardHeight, index);
                            },
                        // Enhanced pagination with smooth animations
                        pagination: widget.slides.length <= 8 ? SwiperPagination(
                          alignment: Alignment.bottomCenter,
                          margin: const EdgeInsets.only(bottom: 100),
                          builder: DotSwiperPaginationBuilder(
                            color: theme.colorScheme.onSurface.withOpacity(0.3),
                            activeColor: theme.colorScheme.primary,
                            size: 6,
                            activeSize: 10,
                            space: 4,
                          ),
                        ) : null,
                          );
                        },
                      ),
                    ),
                  ),
                  
                  // Navigation
                  _buildNavigation(),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final totalSlides = widget.slides.length;
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          IconButton(
            icon: Icon(Icons.close, color: theme.colorScheme.onSurface, size: 20),
            onPressed: () => Navigator.of(context).pop(),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.module.title,
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 12),
          // Compact progress pill (always visible)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurface.withOpacity(0.12),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: theme.colorScheme.onSurface.withOpacity(0.18), width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.slideshow, size: 14, color: theme.colorScheme.onSurface.withOpacity(0.9)),
                const SizedBox(width: 6),
                Text('${currentIndex + 1} / $totalSlides',
                    style: TextStyle(color: theme.colorScheme.onSurface, fontSize: 12, fontWeight: FontWeight.w600)),
                const SizedBox(width: 8),
                Container(
                  width: 40,
                  height: 6,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.onSurface.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(3),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: totalSlides == 0 ? 0 : (currentIndex + 1) / totalSlides,
                    child: Container(
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    final totalSlides = widget.slides.length;
    // Show position-based progress so it advances/retracts as you swipe
    final positionCount = (currentIndex + 1).clamp(0, totalSlides);
    final Color moduleColor = _parseHexColor(widget.module.slidesColor) ?? Theme.of(context).colorScheme.primary;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Clean progress bar
          ProgressBar(
            completedCount: positionCount,
            totalItems: totalSlides,
            progressColor: moduleColor,
            backgroundColor: moduleColor.withOpacity(0.20),
          ),
          const SizedBox(height: 12),
          // Slide indicators (only if 10 or fewer slides)
          SlideIndicators(
            currentIndex: currentIndex,
            totalSlides: totalSlides,
            completedSlideIds: completedSlides,
            slideIds: widget.slides.map((s) => s.id).toList(),
          ),
        ],
      ),
    );
  }

  // Legacy card builder removed in full-bleed mode

  // Legacy builder kept for reference; not used in current flow
  // ignore: unused_element
  Widget _buildCardFront(SlideResponse slide) {
    if (slide.image?.fullUrl != null) {
      // Keep for legacy card mode (not used in full-bleed path)
      return _buildFullBleedSlide(slide);
    }
    
    // Fallback for slides without images
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
             Theme.of(context).colorScheme.primary,
             Theme.of(context).colorScheme.primaryContainer,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getSlideIcon(slide.type),
            size: 64,
            color: Theme.of(context).colorScheme.onPrimary,
          ),
          const SizedBox(height: 24),
          Text(
            slide.title,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          if (slide.content != null) ...[
            const SizedBox(height: 16),
            Icon(
              Icons.touch_app,
              color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.7),
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              'Tap to read more',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.7),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // New: Full-bleed slide renderer (no white card, no cropping)
  Widget _buildFullBleedSlide(SlideResponse slide) {
    if (slide.image?.fullUrl == null) {
      return Center(
        child: Icon(
          Icons.image_not_supported,
          size: 64,
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
        ),
      );
    }
    final url = slide.image!.originalFullUrl.isNotEmpty
        ? slide.image!.originalFullUrl
        : slide.image!.fullUrl;
    return GestureDetector(
      onDoubleTap: () => _showZoomedImage(url),
      child: Stack(
        fit: StackFit.expand,
        children: [
          // Soft, blurred backdrop using the same image (eliminates harsh black bars)
          ImageFiltered(
            imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
            child: Image.network(
              url,
              fit: BoxFit.cover,
              gaplessPlayback: true,
              frameBuilder: (context, child, frame, wasSync) {
                if (wasSync || frame != null) return child;
                // Keep transparent to let the card's own background/tint show through
                return const SizedBox.expand();
              },
              errorBuilder: (context, error, stack) {
                // Transparent on error to avoid mismatched placeholder color
                return const SizedBox.expand();
              },
            ),
          ),
          // Dim overlay to bring focus to the foreground image (theme-aware)
          Container(
            color: Theme.of(context).colorScheme.scrim.withOpacity(
                  Theme.of(context).brightness == Brightness.dark ? 0.35 : 0.12,
                ),
          ),
          // Foreground full image (not cropped)
          Center(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.shadow.withOpacity(0.25),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              clipBehavior: Clip.antiAlias,
              child: Image.network(
                url,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    Icons.image_not_supported,
                    size: 64,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Enhanced swipe card with modern visual effects and animations
Widget _buildEnhancedSwipeCard(SlideResponse slide, double width, double height, int index) {
    final bool isCurrent = index == currentIndex;
    return AnimatedOpacity(
      opacity: isCurrent ? 1 : 0,
      duration: const Duration(milliseconds: 120),
      curve: Curves.easeOut,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeOutCubic,
        // Keep identity transform to avoid peeking/scale artifacts
        transform: Matrix4.identity(),
        child: _buildSwipeCard(slide, width, height),
      ),
    );
  }

  Widget _buildSwipeCard(SlideResponse slide, double width, double height) {
    final Color? containerBg = _parseHexColor(widget.module.slidesColor);
    final url = (slide.image?.originalFullUrl.isNotEmpty ?? false)
        ? slide.image!.originalFullUrl
        : (slide.image?.fullUrl ?? "");

    return Container(
      width: width,
      height: height,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 12), // Add margins around card
      decoration: BoxDecoration(
        color: containerBg ?? Theme.of(context).colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(20), // Consistent rounded corners
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      clipBehavior: Clip.hardEdge,
      child: url.isEmpty
          ? Center(
              child: Icon(
                Icons.image_not_supported,
                size: 48,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.35),
              ),
            )
          : GestureDetector(
              onDoubleTap: () => _showZoomedImage(url),
              child: _buildCachedImage(url),
            ),
    );
  }

  // Enhanced cached image widget that prevents blanking
  Widget _buildCachedImage(String url) {
    return Image.network(
      url,
      fit: BoxFit.cover, // Fill the card to eliminate side gutters
      width: double.infinity,
      height: double.infinity,
      gaplessPlayback: true, // Prevents blanking during transitions
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded || frame != null) {
          return child;
        }
        // Show a centered spinner while preserving the card's own background & radius
        return SizedBox.expand(
          child: Center(
            child: SizedBox(
              width: 28,
              height: 28,
              child: CircularProgressIndicator.adaptive(
                strokeWidth: 1.75,
                valueColor: AlwaysStoppedAnimation(
                  Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ),
        );
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        // Preserve background; only show progress indicator
        return SizedBox.expand(
          child: Center(
            child: SizedBox(
              width: 28,
              height: 28,
              child: CircularProgressIndicator.adaptive(
                strokeWidth: 1.75,
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
                valueColor: AlwaysStoppedAnimation(
                  Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return Container(
          color: Theme.of(context).colorScheme.surface,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.image_not_supported,
                  size: 48,
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                ),
                const SizedBox(height: 8),
                Text(
                  'Image not available',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Card back removed (flip disabled)

  Widget _buildNavigation() {
    final isLastCard = currentIndex == widget.slides.length - 1;
    final currentSlideId = widget.slides[currentIndex].id;
    final isCurrentSlideCompleted = completedSlides.contains(currentSlideId);
    // Check if all slides are actually completed by checking each slide ID
    final allSlidesCompleted = widget.slides.every((slide) => completedSlides.contains(slide.id));
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Slide completion status
          if (!isCurrentSlideCompleted) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: theme.colorScheme.secondaryContainer,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.visibility_outlined,
                    size: 14,
                    color: theme.colorScheme.onSecondaryContainer,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'Viewing slide...',
                    style: theme.textTheme.labelMedium?.copyWith(
                      color: theme.colorScheme.onSecondaryContainer,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
          ],
          
          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Previous button
              Container(
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  onPressed: currentIndex > 0
                      ? () {
                          HapticFeedback.lightImpact();
                          _swiperController.previous();
                        }
                      : null,
                  icon: Icon(
                    Icons.arrow_back_ios,
                    size: 18,
                    color: currentIndex > 0
                        ? theme.colorScheme.onSurface
                        : theme.colorScheme.onSurface.withOpacity(0.3),
                  ),
                ),
              ),
              
              // Main action button
              ElevatedButton(
                onPressed: isLastCard && allSlidesCompleted
                    ? _completeModule
                    : () {
                        HapticFeedback.lightImpact();
                        if (isLastCard) {
                          _completeModule();
                        } else {
                          _swiperController.next();
                        }
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor: isLastCard && allSlidesCompleted
                      ? theme.colorScheme.tertiary
                      : theme.colorScheme.primary,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  elevation: 4,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      isLastCard && allSlidesCompleted
                          ? 'Complete Module'
                          : isLastCard
                              ? 'Finish Review'
                              : 'Next Slide',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: (isLastCard && allSlidesCompleted)
                            ? Theme.of(context).colorScheme.onTertiary
                            : Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      isLastCard && allSlidesCompleted
                          ? Icons.check_circle
                          : isLastCard
                              ? Icons.refresh
                              : Icons.arrow_forward,
                      size: 18,
                      color: (isLastCard && allSlidesCompleted)
                          ? Theme.of(context).colorScheme.onTertiary
                          : Theme.of(context).colorScheme.onPrimary,
                    ),
                  ],
                ),
              ).animate()
                .scale(delay: 300.ms, duration: 400.ms)
                .fadeIn(),
              
              // Skip/Jump to end button
              Container(
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  onPressed: !isLastCard
                      ? () {
                          HapticFeedback.lightImpact();
                          _swiperController.move(widget.slides.length - 1);
                        }
                      : null,
                  icon: Icon(
                    Icons.skip_next,
                    size: 18,
                    color: !isLastCard
                        ? theme.colorScheme.onSurface
                        : theme.colorScheme.onSurface.withOpacity(0.3),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getSlideIcon(String? type) {
    switch (type?.toLowerCase()) {
      case 'quiz':
        return Icons.quiz;
      case 'interaction':
        return Icons.touch_app;
      case 'summary':
        return Icons.summarize;
      default:
        return Icons.article;
    }
  }
}

class _BackgroundPatternPainter extends CustomPainter {
  final Color patternColor;

  _BackgroundPatternPainter({required this.patternColor});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = patternColor
      ..style = PaintingStyle.fill;

    const spacing = 30.0;
    for (double x = 0; x < size.width; x += spacing) {
      for (double y = 0; y < size.height; y += spacing) {
        canvas.drawCircle(Offset(x, y), 2, paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant _BackgroundPatternPainter oldDelegate) =>
      oldDelegate.patternColor != patternColor;
}

// Utility: parse hex color (e.g. '#FFAA00' or 'FFAA00') to Color
Color? _parseHexColor(String? hex) {
  if (hex == null || hex.isEmpty) return null;
  var value = hex.trim();
  if (value.startsWith('#')) value = value.substring(1);
  if (value.length == 6) value = 'FF$value';
  if (value.length != 8) return null;
  try {
    final intColor = int.parse(value, radix: 16);
    return Color(intColor);
  } catch (_) {
    return null;
  }
}

import 'package:flutter/material.dart';
import 'package:skilldeck/models/api_models.dart';
import 'package:skilldeck/services/supabase_rs_service.dart';
import 'package:skilldeck/core/theme/app_theme.dart';
import 'package:skilldeck/screens/module_slides_swiper.dart';
// import 'package:skilldeck/screens/module_reels_viewer.dart';

class ModuleDetailPageApi extends StatefulWidget {
  final String courseId;
  final ModuleResponse module;

  const ModuleDetailPageApi({
    super.key,
    required this.courseId,
    required this.module,
  });

  @override
  State<ModuleDetailPageApi> createState() => _ModuleDetailPageApiState();
}

class _ModuleDetailPageApiState extends State<ModuleDetailPageApi> {
  ModuleResponse? _detailedModule;
  List<SlideResponse> _slides = [];
  bool _isLoading = true;
  String? _error;
  int _currentSlideIndex = 0;
  late final SupabaseRsService _supa;

  @override
  void initState() {
    super.initState();
    _supa = SupabaseRsService();
    _loadModuleDetails();
  }

  Future<void> _loadModuleDetails() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load module with slides using Supabase
      final moduleRes = await _supa.getModuleDetail(widget.module.id);
      if (!moduleRes.success || moduleRes.data == null) {
        setState(() {
          _error = moduleRes.error ?? 'Failed to load module';
          _isLoading = false;
        });
        return;
      }

      final module = moduleRes.data!;
      // ALWAYS fetch slides via bulk endpoint to ensure images are expanded
      // The module endpoint doesn't fully expand nested relations
      List<SlideResponse> slides = [];
      
      // Get slide IDs - they could be numbers or expanded objects
      List<dynamic> slideIds = [];
      if (module.slides is List) {
        for (var slide in (module.slides as List)) {
          if (slide is Map) {
            slideIds.add(slide['id'] ?? 0);
          } else {
            slideIds.add(slide);
          }
        }
      }
      
      // Always fetch via bulk endpoint for proper image expansion
      if (slideIds.isNotEmpty) {
        final slidesRes = await _supa.getSlides(slideIds);
        if (slidesRes.success && slidesRes.data != null) {
          slides = slidesRes.data!;
        }
      }

      // Debug: confirm images are present
      debugPrint('[UI] slides loaded: ${slides.length}, first=${slides.isNotEmpty ? slides.first.id : 'n/a'} image=${slides.isNotEmpty ? (slides.first.image?.fullUrl ?? 'null') : 'n/a'}');

      setState(() {
        _detailedModule = module;
        _slides = slides;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  // Navigation is handled by the swiper in the card-based UI

  @override
  Widget build(BuildContext context) {
    final module = _detailedModule ?? widget.module;
    
    // If slides are loaded, render the enhanced swipe experience
    if (!_isLoading && _error == null && _slides.isNotEmpty) {
      // Use enhanced ModuleSlidesSwiper with modern swipe features
      return ModuleSlidesSwiper(
        courseId: widget.courseId,
        module: module,
        slides: _slides,
      );
    }

    // Loading: show neutral background + skeleton (no AppBar to avoid flicker)
    if (_isLoading) {
      return Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        body: _buildLoadingSkeleton(context, module.title),
      );
    }

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.secondary,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              module.title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (!_isLoading && _slides.isNotEmpty)
              Text(
                'Slide ${_currentSlideIndex + 1} of ${_slides.length}',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.7),
                  fontSize: 12,
                ),
              ),
          ],
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorView()
              : _buildEmptyView(),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.warningRed,
            ),
            const SizedBox(height: 16),
            Text(
              _error ?? 'Something went wrong',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadModuleDetails,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.safetyOrange,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assignment_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No slides available for this module',
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingSkeleton(BuildContext context, String title) {
    final theme = Theme.of(context);
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header placeholder matching `ModuleSlidesSwiper._buildHeader`
            Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                // Module title placeholder
                Expanded(
                  child: Container(
                    height: 18,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // Progress pill placeholder
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.08),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: theme.colorScheme.onSurface.withValues(alpha: 0.12), width: 1),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.slideshow, size: 14, color: theme.colorScheme.onSurface.withValues(alpha: 0.6)),
                      const SizedBox(width: 6),
                      Container(
                        width: 50,
                        height: 8,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Progress bar placeholder matching `ProgressBar`
            Container(
              height: 6,
              width: double.infinity,
              decoration: BoxDecoration(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.08),
                borderRadius: BorderRadius.circular(3),
              ),
            ),
            const SizedBox(height: 12),
            // Slide indicators placeholder (dots) when few slides — use a thin bar
            Container(
              height: 6,
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 8),
              decoration: BoxDecoration(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.06),
                borderRadius: BorderRadius.circular(3),
              ),
            ),
            const SizedBox(height: 8),
            // Full-bleed single slide skeleton to match swiper's card
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  final cardWidth = constraints.maxWidth;
                  final cardHeight = constraints.maxHeight * 0.90;
                  final containerBg = theme.colorScheme.surfaceContainerHigh;
                  return Center(
                    child: Container(
                      width: cardWidth,
                      height: cardHeight,
                      decoration: BoxDecoration(
                        color: containerBg,
                        borderRadius: BorderRadius.all(
                          Radius.elliptical(cardWidth * 0.05, cardHeight * 0.05),
                        ),
                      ),
                      clipBehavior: Clip.hardEdge,
                      child: Stack(
                        fit: StackFit.expand,
                        children: const [
                          // Center spinner like image loading state
                          Center(child: CircularProgressIndicator()),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 8),
            // Bottom navigation skeleton matching three controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Prev circle
                Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.10),
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                // Main action button
                Expanded(
                  child: Container(
                    height: 48,
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: AppColors.safetyOrange.withValues(alpha: 0.35),
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                ),
                // Skip circle
                Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.10),
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:skilldeck/models/api_models.dart';
import 'package:skilldeck/services/supabase_rs_service.dart';
import 'package:skilldeck/services/progress_service.dart';
import 'package:skilldeck/screens/course_detail_page_api.dart';
import 'package:skilldeck/screens/modules_page.dart';
import 'package:skilldeck/screens/practice_page.dart';
// import 'package:skilldeck/screens/search_page.dart';
import 'package:skilldeck/core/theme/app_theme.dart';
import 'package:skilldeck/widgets/app_page_scaffold.dart';
import 'package:skilldeck/widgets/universal_search_bar.dart';

class HomePageClean extends StatefulWidget {
  const HomePageClean({super.key});

  @override
  State<HomePageClean> createState() => _HomePageCleanState();
}

class _HomePageCleanState extends State<HomePageClean> {
  final TextEditingController _searchController = TextEditingController();
  final ProgressService _progressService = ProgressService();
  
  List<CourseResponse> courses = [];
  List<CourseResponse> filteredCourses = [];
  Map<String, double> courseProgressMap = {};
  late final SupabaseRsService _supa;
  int _currentIndex = 0;
  bool _isLoading = true;
  String? _error;
  
  final List<Color> _cardColors = [
    AppColors.safetyOrange,
    AppColors.highVisYellow,
    AppColors.warningRed,
    AppColors.industrialBlue,
    AppColors.constructionGreen,
  ];

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_handleSearch);
    _supa = SupabaseRsService();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _handleSearch() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        filteredCourses = courses;
      } else {
        // Simple qualifiers: title: and desc: or full slide search with slide:
        String q = query;
        bool titleOnly = false;
        bool descOnly = false;
        if (q.startsWith('title:')) { titleOnly = true; q = q.substring(6).trim(); }
        if (q.startsWith('desc:')) { descOnly = true; q = q.substring(5).trim(); }
        final qLower = q.toLowerCase();

        filteredCourses = courses.where((course) {
          final inTitle = course.title.toLowerCase().contains(qLower);
          final inDesc = (course.description?.toLowerCase().contains(qLower) ?? false);
          if (titleOnly) return inTitle;
          if (descOnly) return inDesc;
          return inTitle || inDesc;
        }).toList();
      }
    });
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await _supa.getCourses(limit: 50);
      if (!mounted) return;
      if (response.success && response.data != null) {
        // Load progress for each course
        final progressMap = <String, double>{};
        final userProgress = await _progressService.getUserProgress();
        
        for (final course in response.data!) {
          final courseProgress = userProgress.courseProgress[course.id];
          if (courseProgress != null) {
            // Calculate course progress based on modules
            final courseDetail = await _supa.getCourseDetail(course.id);
            if (courseDetail.success && courseDetail.data != null) {
              int totalSlides = 0;
              int completedSlides = 0;
              
              for (final module in courseDetail.data!.expandedModules) {
                totalSlides += module.slideCount;
                final moduleProgress = courseProgress.moduleProgress[module.id];
                if (moduleProgress != null) {
                  completedSlides += moduleProgress.completedCardIds.length;
                }
              }
              
              progressMap[course.id] = totalSlides > 0 
                  ? completedSlides / totalSlides 
                  : 0;
            }
          } else {
            progressMap[course.id] = 0;
          }
        }
        
        setState(() {
          courses = response.data!;
          filteredCourses = courses;
          courseProgressMap = progressMap;
          _isLoading = false;
        });
        return;
      }
      setState(() {
        _error = response.error ?? 'Failed to load courses';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
        _isLoading = false;
      });
    }
  }
  
  // Mock disabled: keep function removed to avoid fallback paths

  // Refresh is handled by pull-to-refresh in the course list

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: [
          _buildHomePage(),
          const ModulesPage(),
          const PracticePage(),
        ],
      ),
      bottomNavigationBar: _buildBottomNav(),
    );
  }

  Widget _buildBottomNav() {
    final theme = Theme.of(context);
    // final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.12),
            blurRadius: 20,
            spreadRadius: 5,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
        child: Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerLowest,
            border: Border(
              top: BorderSide(
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
          ),
          child: SafeArea(
            top: false,
            child: BottomNavigationBar(
              currentIndex: _currentIndex,
              onTap: (i) => setState(() => _currentIndex = i),
              items: const [
                BottomNavigationBarItem(
                  icon: Icon(Icons.home_outlined),
                  activeIcon: Icon(Icons.home),
                  label: 'Home',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.view_module_outlined),
                  activeIcon: Icon(Icons.view_module),
                  label: 'Modules',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.receipt_long_outlined),
                  activeIcon: Icon(Icons.receipt_long),
                  label: 'Practice',
                ),
              ],
              type: BottomNavigationBarType.fixed,
              backgroundColor: Colors.transparent,
              elevation: 0,
              selectedItemColor: AppColors.safetyOrange,
              unselectedItemColor: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHomePage() {
    return AppPageScaffold(
      title: 'RS Plus',
      subtitle: 'Work Zone Safety',
      headerBelow: null,
       searchBar: _buildSearchBar(),
      slivers: [
        // Quick stats section
        _buildQuickStats(),
        
        // Featured/Recent section
        _buildFeaturedSection(),
        
        // Course list
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          sliver: _isLoading
              ? _buildLoadingList()
              : _error != null
                  ? _buildErrorView()
                  : _buildCourseList(),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return const UniversalSearchBar(
      hintText: 'Search all slides, modules, courses...',
    );
  }



  Widget _buildQuickStats() {
    final totalCourses = courses.length;
    final completedCourses = courseProgressMap.values.where((p) => p >= 0.99).length;
    final inProgressCourses = courseProgressMap.values.where((p) => p > 0 && p < 0.99).length;
    
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(24),
        child: Row(
          children: [
            Expanded(
              child: _buildStatCard(
                icon: Icons.school_outlined,
                title: '$totalCourses',
                subtitle: 'Total Courses',
                color: AppColors.industrialBlue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                icon: Icons.trending_up,
                title: '$inProgressCourses',
                subtitle: 'In Progress',
                color: AppColors.safetyOrange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                icon: Icons.check_circle_outline,
                title: '$completedCourses',
                subtitle: 'Completed',
                color: AppColors.constructionGreen,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          Text(
            subtitle,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedSection() {
    final theme = Theme.of(context);
    
    // Find courses in progress or recent ones
    final featuredCourses = courses.where((course) {
      final progress = courseProgressMap[course.id] ?? 0;
      return progress > 0 && progress < 0.99;
    }).take(2).toList();
    
    if (featuredCourses.isEmpty) {
      return const SliverToBoxAdapter(child: SizedBox.shrink());
    }
    
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(24, 0, 24, 16),
            child: Row(
              children: [
                Icon(
                  Icons.play_circle_outline,
                  color: AppColors.safetyOrange,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Continue Learning',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 24),
              itemCount: featuredCourses.length,
              itemBuilder: (context, index) {
                final course = featuredCourses[index];
                final progress = courseProgressMap[course.id] ?? 0;
                
                return Container(
                  width: 280,
                  margin: EdgeInsets.only(right: index < featuredCourses.length - 1 ? 16 : 0),
                  child: _buildFeaturedCard(course, progress),
                );
              },
            ),
          ),
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.fromLTRB(24, 0, 24, 16),
            child: Row(
              children: [
                Icon(
                  Icons.library_books_outlined,
                  color: AppColors.industrialBlue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'All Courses',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedCard(CourseResponse course, double progress) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CourseDetailPageApi(course: course),
          ),
        ).then((_) => _loadData());
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.safetyOrange.withValues(alpha: 0.1),
              AppColors.safetyOrange.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.safetyOrange.withValues(alpha: 0.2),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      course.title,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.safetyOrange.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${(progress * 100).toInt()}%',
                      style: TextStyle(
                        color: AppColors.safetyOrange,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: progress,
                backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
                valueColor: AlwaysStoppedAnimation(AppColors.safetyOrange),
                minHeight: 4,
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.play_arrow,
                    size: 16,
                    color: AppColors.safetyOrange,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Continue',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppColors.safetyOrange,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${course.moduleCount} modules',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Removed unused `_buildSearchChip` helper to avoid linter warningRRRR

  Widget _buildLoadingList() {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) => _buildSkeletonCard(),
        childCount: 3,
      ),
    );
  }

  Widget _buildSkeletonCard() {
    final theme = Theme.of(context);
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      height: 120,
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(20),
      ),
    );
  }

  Widget _buildErrorView() {
    return SliverFillRemaining(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.warningRed,
            ),
            const SizedBox(height: 16),
            Text(
              _error ?? 'Something went wrong',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadData,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.safetyOrange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCourseList() {
    if (filteredCourses.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 64,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(height: 16),
              Text(
                _searchController.text.isNotEmpty
                    ? 'No courses found matching your search'
                    : 'No courses available',
                style: TextStyle(
                  fontSize: 16,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final course = filteredCourses[index];
          final cardColor = _cardColors[index % _cardColors.length];
          
          return _buildCourseCard(course, cardColor, index);
        },
        childCount: filteredCourses.length,
      ),
    );
  }

  Widget _buildCourseCard(CourseResponse course, Color cardColor, int index) {
    final theme = Theme.of(context);
    final progress = courseProgressMap[course.id] ?? 0;
    final isCompleted = progress >= 0.99;
    final isInProgress = progress > 0 && !isCompleted;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CourseDetailPageApi(course: course),
            ),
          ).then((_) => _loadData());
        },
        child: Card(
          elevation: 2,
          shadowColor: theme.colorScheme.onSurface.withValues(alpha: 0.1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
            side: BorderSide(
              color: theme.colorScheme.outline.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    // Clean thumbnail without progress ring
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Container(
                        width: 64,
                        height: 64,
                        color: theme.colorScheme.surfaceContainerHighest,
                        child: course.courseThumbnail?.fullUrl != null
                            ? Image.network(
                                course.courseThumbnail!.fullUrl,
                                fit: BoxFit.cover,
                              )
                            : Icon(
                                Icons.image_outlined,
                                size: 28,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  course.title,
                                  style: theme.textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (isCompleted)
                                Icon(
                                  Icons.check_circle,
                                  color: AppColors.constructionGreen,
                                  size: 20,
                                )
                              else if (isInProgress)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.safetyOrange.withValues(alpha: 0.15),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '${(progress * 100).toInt()}%',
                                    style: TextStyle(
                                      color: AppColors.safetyOrange,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 6),
                          if (course.description != null)
                            Text(
                              course.description!,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                                height: 1.4,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              _Chip(text: '${course.moduleCount} modules'),
                              const SizedBox(width: 8),
                              _Chip(text: '${course.expandedModules.fold(0, (sum, m) => sum + m.slideCount)} slides'),
                              const Spacer(),
                              Icon(
                                Icons.arrow_forward_ios,
                                size: 16,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              // Progress bar at bottom
              if (isInProgress) ...[
                Container(
                  height: 4,
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.outline.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: progress,
                    child: Container(
                      decoration: BoxDecoration(
                        color: isCompleted ? AppColors.constructionGreen : AppColors.safetyOrange,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ] else
                const SizedBox(height: 4),
            ],
          ),
        ),
      ),
    );
  }
}

class _Chip extends StatelessWidget {
  final String text;
  const _Chip({required this.text});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 30 / 255),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: theme.textTheme.labelSmall?.copyWith(
          color: theme.colorScheme.onSurfaceVariant,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
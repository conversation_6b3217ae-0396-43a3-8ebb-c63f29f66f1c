import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:skilldeck/core/theme/app_theme.dart';
import 'package:skilldeck/widgets/app_page_scaffold.dart';
import 'package:skilldeck/widgets/universal_search_bar.dart';
// import 'package:skilldeck/screens/interaction_webview.dart'; // Removed unused webview

class PracticePage extends StatefulWidget {
  const PracticePage({super.key});

  @override
  State<PracticePage> createState() => _PracticePageState();
}

// Interactive content model
class InteractiveContent {
  final int id;
  final String title;
  final String description;
  final String type;
  final IconData icon;
  final Color color;
  final String? webUrl;

  InteractiveContent({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.icon,
    required this.color,
    this.webUrl,
  });
}

class _PracticePageState extends State<PracticePage> with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  List<InteractiveContent> _allInteractions = [];
  List<InteractiveContent> _filteredInteractions = [];
  late AnimationController _refreshController;
  late AnimationController _cardAnimationController;
  int _selectedIndex = -1;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_handleSearch);
    _refreshController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _refreshController.dispose();
    _cardAnimationController.dispose();
    super.dispose();
  }

  void _handleSearch() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredInteractions = _allInteractions;
      } else {
        _filteredInteractions = _allInteractions.where((interaction) {
          return interaction.title.toLowerCase().contains(query) ||
                 interaction.description.toLowerCase().contains(query) ||
                 interaction.type.toLowerCase().contains(query);
        }).toList();
      }
    });
  }

  Future<void> _loadData() async {
    _refreshController.forward();
    
    // Simulate loading delay for better UX
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Load interactive content
    _allInteractions = [
      InteractiveContent(
        id: 1,
        title: '3D Traffic Control Setup',
        description: 'Interactive 3D model of traffic control zone setup',
        type: 'model',
        icon: Icons.view_in_ar,
        color: AppColors.constructionGreen,
        webUrl: 'https://main.d2rxiecdlxz8ub.amplifyapp.com/',
      ),
      InteractiveContent(
        id: 2,
        title: 'Flagger Animation Sequence',
        description: 'Learn proper flagging techniques through interactive animation',
        type: 'animation',
        icon: Icons.animation,
        color: AppColors.highVisYellow,
        webUrl: 'https://main.d2rxiecdlxz8ub.amplifyapp.com/',
      ),
      InteractiveContent(
        id: 3,
        title: 'Safety Equipment Assembly',
        description: 'Assemble and configure safety equipment components',
        type: 'assembly',
        icon: Icons.build_circle,
        color: AppColors.safetyOrange,
        webUrl: 'https://main.d2rxiecdlxz8ub.amplifyapp.com/',
      ),
      InteractiveContent(
        id: 4,
        title: 'Virtual Work Zone Walkthrough',
        description: 'Navigate through a virtual work zone environment',
        type: 'simulation',
        icon: Icons.explore,
        color: AppColors.industrialBlue,
        webUrl: 'https://main.d2rxiecdlxz8ub.amplifyapp.com/',
      ),
      InteractiveContent(
        id: 5,
        title: 'Equipment Inspection Checklist',
        description: 'Interactive checklist for safety equipment inspection',
        type: 'checklist',
        icon: Icons.checklist,
        color: AppColors.warningRed,
        webUrl: 'https://main.d2rxiecdlxz8ub.amplifyapp.com/',
      ),
      InteractiveContent(
        id: 6,
        title: 'Emergency Response Drill',
        description: 'Practice emergency response procedures in simulated scenarios',
        type: 'drill',
        icon: Icons.emergency,
        color: AppColors.constructionGreen,
        webUrl: 'https://main.d2rxiecdlxz8ub.amplifyapp.com/',
      ),
    ];
    
    _filteredInteractions = _allInteractions;
    
    if (mounted) {
      setState(() {});
      // Do not reset the controller here; resetting to 0 makes items fully transparent
      // Keep value at 1 so list items remain visible after load
    }
  }

  void _onInteractionTap(InteractiveContent interaction) {
    HapticFeedback.lightImpact();
    _showInteractiveDialog(interaction);
  }

  void _showInteractiveDialog(InteractiveContent interaction) {
    final theme = Theme.of(context);
    final Color accentColor = theme.colorScheme.onSurfaceVariant;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: accentColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(interaction.icon, color: accentColor, size: 24),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                interaction.title,
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(interaction.description),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: accentColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                interaction.type.toUpperCase(),
                style: TextStyle(
                  color: accentColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _launchInteractiveContent(interaction);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text('Launch'),
          ),
        ],
      ),
    );
  }

  void _launchInteractiveContent(InteractiveContent interaction) {
    if (interaction.webUrl == null || interaction.webUrl!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('No URL configured for ${interaction.title}')),
      );
      return;
    }
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => Scaffold(
          appBar: AppBar(title: Text(interaction.title)),
          body: const Center(
            child: Text('Interactive features coming soon!'),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AppPageScaffold(
        title: 'Interactions',
        subtitle: 'Interactive learning experiences',
        searchBar: const UniversalSearchBar(
          hintText: 'Search all slides, modules, courses...',
        ),
        onRefresh: _loadData,
        slivers: [
        // Interactive content section header
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(24, 16, 24, 16),
            child: Row(
              children: [
                Text(
                  'Interactive Modules',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                   color: AppColors.constructionGreen.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${_filteredInteractions.length} activities',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppColors.constructionGreen,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        
        // Interactive content list
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final interaction = _filteredInteractions[index];
                // Keep list items visible regardless of animation state
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: _buildInteractionCard(context, interaction, theme, index),
                );
              },
              childCount: _filteredInteractions.length,
            ),
          ),
        ),
        
        const SliverToBoxAdapter(child: SizedBox(height: 80)),
        ],
      );
  }

  Widget _buildInteractionCard(BuildContext context, InteractiveContent interaction, ThemeData theme, int index) {
    final isDark = theme.brightness == Brightness.dark;
    final isSelected = _selectedIndex == index;
    final Color accentColor = theme.colorScheme.onSurfaceVariant;
    
    return AnimatedScale(
      scale: isSelected ? 0.95 : 1.0,
      duration: const Duration(milliseconds: 150),
      child: Container(
        decoration: BoxDecoration(
           color: isDark ? Colors.white.withValues(alpha: 0.05) : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: accentColor.withValues(alpha: 0.12),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: isDark ? 0.1 : 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              setState(() => _selectedIndex = index);
              _cardAnimationController.forward().then((_) {
                _cardAnimationController.reverse();
                setState(() => _selectedIndex = -1);
              });
              _onInteractionTap(interaction);
            },
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Interactive thumbnail
                  Container(
                    width: 64,
                    height: 64,
                    decoration: BoxDecoration(
                      color: accentColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: accentColor.withValues(alpha: 0.2),
                      ),
                    ),
                    child: Icon(
                      interaction.icon,
                      size: 32,
                      color: accentColor,
                    ),
                  ),
                  const SizedBox(width: 16),
                  
                  // Content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          interaction.title,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 6),
                        Text(
                          interaction.description,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                            height: 1.3,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                          color: accentColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            interaction.type.toUpperCase(),
                            style: TextStyle(
                              color: accentColor,
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // Chevron
                  Container(
                    width: 24,
                    height: 24,
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.chevron_right,
                      size: 20,
                      color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
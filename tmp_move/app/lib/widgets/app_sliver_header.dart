import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:skilldeck/screens/settings_page.dart';

class AppSliverHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final bool showSettings;
  final Widget? customAction;

  const AppSliverHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.showSettings = true,
    this.customAction,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // In dark mode, keep header dark; in light mode use brand/secondary.
    final Color headerColor = isDark
        ? theme.colorScheme.surface
        : theme.colorScheme.secondary;
    final Color headerOnColor = isDark
        ? theme.colorScheme.onSurface
        : theme.colorScheme.onSecondary;

    // Better height management
    final double toolbarHeight = 56.0;
    final double expandedHeight = subtitle != null ? 100.0 : toolbarHeight;

    return SliverAppBar(
      pinned: true,
      floating: false,
      snap: false,
      expandedHeight: expandedHeight,
      toolbarHeight: toolbarHeight,
      centerTitle: false,
      scrolledUnderElevation: 0,
      backgroundColor: headerColor,
      surfaceTintColor: Colors.transparent,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarBrightness: isDark ? Brightness.light : Brightness.dark,
        statusBarIconBrightness: isDark ? Brightness.dark : Brightness.light,
      ),
      titleSpacing: 24,
      flexibleSpace: FlexibleSpaceBar(
        titlePadding: const EdgeInsets.only(left: 24, bottom: 16),
        centerTitle: false,
        title: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: headerOnColor,
                letterSpacing: 0,
              ),
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 2),
              Text(
                subtitle!,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: headerOnColor.withValues(alpha: 0.8),
                ),
              ),
            ],
          ],
        ),
        background: Container(
          color: headerColor,
        ),
      ),
      actions: [
        if (customAction != null) customAction!,
        if (showSettings)
          IconButton(
            tooltip: 'Settings',
            icon: Icon(
              Icons.settings_outlined,
              color: headerOnColor,
            ),
            onPressed: () => Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const SettingsPage()),
            ),
          ),
      ],
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(0.5),
        child: Container(
          height: 0.5,
          color: headerOnColor.withValues(alpha: 0.12),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:skilldeck/widgets/app_sliver_header.dart';
import 'package:skilldeck/core/theme/app_theme.dart';

/// A consistent page scaffold that ensures uniform layout across all pages
class AppPageScaffold extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? searchBar;
  final List<Widget> slivers;
  final bool showSettings;
  final Widget? floatingActionButton;
  final Widget? bottomNavigationBar;
  final EdgeInsets contentPadding;
  // Optional widget rendered below the header and above the search bar
  final Widget? headerBelow;
  // Optional pull-to-refresh handler; when provided, the scroll view is wrapped in a RefreshIndicator
  final Future<void> Function()? onRefresh;

  const AppPageScaffold({
    super.key,
    required this.title,
    this.subtitle,
    this.searchBar,
    required this.slivers,
    this.showSettings = true,
    this.floatingActionButton,
    this.bottomNavigationBar,
    this.contentPadding = const EdgeInsets.symmetric(horizontal: 24),
    this.headerBelow,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // final isDark = theme.brightness == Brightness.dark;
    
    final scrollView = CustomScrollView(
      physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
      slivers: [
        // Consistent header across all pages
        AppSliverHeader(
          title: title,
          subtitle: subtitle,
          showSettings: showSettings,
        ),
        // Optional content directly under the header (e.g., key stat banner)
        if (headerBelow != null)
          SliverToBoxAdapter(
            child: Padding(
               padding: contentPadding.copyWith(top: 12, bottom: 0),
              child: headerBelow,
            ),
          ),
        
        // Search bar with consistent spacing
        if (searchBar != null)
          SliverToBoxAdapter(
            child: Padding(
               padding: contentPadding.copyWith(top: 12, bottom: 16),
              child: searchBar,
            ),
          ),
        
         // Content slivers
        ...slivers,
        
        // Bottom spacing for nav bar
        if (bottomNavigationBar != null)
          const SliverToBoxAdapter(
            child: SizedBox(height: 100),
          ),
      ],
    );

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: onRefresh != null
          ? RefreshIndicator(
              onRefresh: onRefresh!,
              child: scrollView,
            )
          : scrollView,
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
    );
  }
}

/// Consistent search bar widget used across pages
class AppSearchBar extends StatelessWidget {
  final String hintText;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onTap;
  final bool readOnly;
  final bool autofocus;

  const AppSearchBar({
    super.key,
    this.hintText = 'Search...',
    this.controller,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.readOnly = false,
    this.autofocus = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      decoration: BoxDecoration(
        color: isDark 
            ? theme.colorScheme.surfaceContainerHighest
            : theme.colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.08),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.08),
            blurRadius: 12,
            spreadRadius: 0,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        onChanged: onChanged,
        onSubmitted: onSubmitted,
        onTap: onTap,
        readOnly: readOnly,
        autofocus: autofocus,
        style: theme.textTheme.bodyMedium?.copyWith(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
            fontSize: 16,
          ),
          prefixIcon: Container(
            padding: const EdgeInsets.all(12),
            child: Icon(
              Icons.search_rounded,
              color: AppColors.safetyOrange.withValues(alpha: 0.8),
              size: 24,
            ),
          ),
          suffixIcon: readOnly ? Container(
            padding: const EdgeInsets.all(12),
            child: Icon(
              Icons.tune_rounded,
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
              size: 20,
            ),
          ) : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
        ),
      ),
    );
  }
}

/// Section header widget for consistent section titles
class AppSectionHeader extends StatelessWidget {
  final String title;
  final String? actionText;
  final VoidCallback? onActionTap;

  const AppSectionHeader({
    super.key,
    required this.title,
    this.actionText,
    this.onActionTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          if (actionText != null)
            GestureDetector(
              onTap: onActionTap,
              child: Text(
                actionText!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
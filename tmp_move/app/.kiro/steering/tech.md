# SkillDeck Technical Stack

## Framework & Language
- **Flutter** (SDK ^3.6.0) - Cross-platform mobile development
- **Dart** - Primary programming language

## Key Dependencies
- **provider** ^6.1.5 - State management
- **http** ^1.2.0 - API communication
- **shared_preferences** ^2.0.0 - Local data persistence
- **flutter_secure_storage** ^9.2.2 - Secure token storage
- **card_swiper** ^3.0.1 - Interactive card swiping
- **flutter_animate** ^4.5.2 - UI animations

## Architecture Patterns
- **Provider Pattern** - State management with ChangeNotifier
- **Service Layer** - Separation of business logic (ApiService, ProgressService)
- **Repository Pattern** - Data access abstraction
- **Flavor Configuration** - Environment-specific builds

## Build System & Commands

### Environment Flavors
The app supports three build flavors:
- **Development** - `lib/main_development.dart`
- **Staging** - `lib/main_staging.dart` 
- **Production** - `lib/main_production.dart`

### Common Commands
```bash
# Setup
make get                    # Install dependencies
make clean                  # Clean build artifacts

# Development
make run-dev               # Run development build
make watch                 # Hot reload development

# Building
make build-dev             # Build development APK
make build-staging         # Build staging APK
make build-prod           # Build production APK

# iOS builds
make build-ios-dev        # Build development iOS
make build-ios-staging    # Build staging iOS
make build-ios-prod       # Build production iOS

# Code Quality
make test                 # Run tests
make analyze              # Static analysis
make format               # Format code (100 char line length)
```

## API Integration
- JWT-based authentication with automatic token refresh
- RESTful API with timeout handling (20s default)
- Graceful error handling and offline fallbacks
- Development credentials for testing endpoints
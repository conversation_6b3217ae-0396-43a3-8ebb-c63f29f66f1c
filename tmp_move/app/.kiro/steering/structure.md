# SkillDeck Project Structure

## Root Directory Organization

```
lib/
├── main.dart                    # Default entry point (development)
├── main_development.dart        # Development flavor entry
├── main_staging.dart           # Staging flavor entry
├── main_production.dart        # Production flavor entry
└── app.dart                    # Main app widget with providers
```

## Core Architecture Folders

### `/lib/config/`
- `flavor_config.dart` - Environment configuration (dev/staging/prod)

### `/lib/core/`
- `config/api_config.dart` - API endpoints and configuration
- `theme/app_theme.dart` - Material theme definitions

### `/lib/models/`
Data models and DTOs:
- `course.dart` - Course entity
- `module.dart` - Module entity  
- `learning_card.dart` - Learning card content
- `user_progress.dart` - Progress tracking
- `api_models.dart` - API response models

### `/lib/services/`
Business logic and external integrations:
- `api_service.dart` - HTTP API client with JWT auth
- `auth_service.dart` - Authentication logic
- `data_service.dart` - Local data management
- `progress_service.dart` - Progress tracking
- `supabase_rs_service.dart` - Active Supabase integration

### `/lib/providers/`
State management:
- `theme_provider.dart` - Theme switching logic

### `/lib/screens/`
Full-screen pages:
- `home_page_clean.dart` - Main home page with course listing
- `course_detail_page_api.dart` - Course detail view with API integration
- `module_detail_page_api.dart` - Module detail view with API integration
- `learning_card_page.dart` - Card learning interface
- `module_slides_swiper.dart` - Swipeable module content
- `modules_page.dart` - All modules listing
- `browse_page.dart` - Content browsing
- `practice_page.dart` - Practice mode
- `settings_page.dart` - App settings
- `login_screen.dart` - Authentication screen

### `/lib/widgets/`
Reusable UI components:
- `course_card.dart` - Course display widget
- `module_card.dart` - Module display widget
- `swipeable_card.dart` - Interactive learning card
- `progress_*.dart` - Progress indicators
- `app_*.dart` - Common app components

### `/lib/utils/`
Utility functions and test data:
- `test_progress_data.dart` - Mock data for development

## Naming Conventions

- **Files**: snake_case (e.g., `course_detail_page.dart`)
- **Classes**: PascalCase (e.g., `CourseDetailPage`)
- **Variables/Functions**: camelCase (e.g., `getUserProgress`)
- **Constants**: SCREAMING_SNAKE_CASE (e.g., `API_BASE_URL`)

## File Suffixes

- `*_page.dart` - Full screen pages
- `*_service.dart` - Business logic services  
- `*_provider.dart` - State management providers
- `*_card.dart` - Card-style widgets
- `*_api.dart` - API-integrated versions of screens
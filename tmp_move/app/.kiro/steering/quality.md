# SkillDeck Quality Standards

## Code Quality Guidelines

### Performance Optimization
- Minimize widget rebuilds by using `const` constructors where possible
- Use `ValueKey` for widgets that need specific rebuild behavior
- Implement proper loading states to avoid UI blocking
- Cache expensive computations and API responses when appropriate
- Use `ListView.builder` for large lists instead of `ListView`

### Memory Management
- Dispose of controllers, streams, and listeners in `dispose()` method
- Use weak references for callbacks to prevent memory leaks
- Check `mounted` before calling `setState()` in async operations
- Avoid retaining references to disposed widgets

### Error Resilience
- Implement comprehensive error handling for all async operations
- Provide meaningful error messages to users
- Use fallback UI states when data loading fails
- Log errors appropriately for debugging without exposing sensitive data

## Testing Considerations

### Testable Code Structure
- Keep business logic separate from UI code
- Use dependency injection for services
- Make widgets testable by accepting data through constructors
- Avoid direct API calls in widget code - use service layer

### Mock Data Support
- Maintain test data in `utils/test_progress_data.dart`
- Support offline development with mock responses
- Use flavor configuration to switch between real and mock data

## Security Best Practices

### Data Protection
- Use `flutter_secure_storage` for sensitive data (JWT tokens)
- Never hardcode API keys or secrets in source code
- Validate all user inputs before processing
- Implement proper authentication flows

### API Security
- Use JWT tokens with automatic refresh
- Implement proper timeout handling (20s default)
- Handle 401 responses gracefully with re-authentication
- Use HTTPS for all API communications

## Accessibility Standards

### UI Accessibility
- Provide semantic labels for interactive elements
- Ensure proper contrast ratios for text and backgrounds
- Support screen readers with appropriate widget semantics
- Make touch targets at least 44x44 points

### User Experience
- Implement consistent navigation patterns
- Provide clear visual feedback for user actions
- Support both light and dark themes
- Ensure app works offline for core functionality